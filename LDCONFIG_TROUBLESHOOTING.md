# TECO Container Runtime - ldconfig 权限问题解决方案

## 问题描述

在使用 teco-container-runtime 启动挂载显卡的容器时，可能会遇到 ldconfig 权限错误：

```
/sbin/ldconfig: Can't link /usr/lib/libsddev.so to libsddev.so.2.1.1
/sbin/ldconfig: Can't unlink /usr/lib/libteco-ml.so
/sbin/ldconfig: Changing access rights of /etc/ld.so.cache~ to 0644 failed: Operation not permitted
```

## 根本原因

1. **安全限制**：teco-container-runtime 为了安全性，在执行 ldconfig 时创建了受限的执行环境
2. **文件系统权限**：某些关键目录被挂载为只读，限制了 ldconfig 的文件操作权限
3. **权限能力不足**：ldconfig 进程缺少必要的 Linux capabilities 来执行文件系统操作

## 解决方案

### 方案1：使用环境变量（推荐）

设置环境变量来启用 ldconfig 宽松模式：

```bash
export NVC_LDCONFIG_PERMISSIVE=1
```

然后重新运行容器命令：

```bash
NVC_LDCONFIG_PERMISSIVE=1 /opt/kube/bin/containerd-bin/nerdctl run -it --rm \
  --network=host --namespace=k8s.io \
  --security-opt seccomp=unconfined \
  --runtime=/usr/bin/teco-container-runtime \
  -e TECO_VISIBLE_DEVICES=all \
  registry.tecorigin.io:5443/loong64/anolisos:v8.9 \
  bash
```

### 方案1.1：系统级环境变量设置

如果需要永久生效，可以在系统级别设置环境变量：

```bash
# 添加到 /etc/environment
echo "NVC_LDCONFIG_PERMISSIVE=1" >> /etc/environment

# 或者添加到 systemd 服务配置中
mkdir -p /etc/systemd/system/containerd.service.d/
cat > /etc/systemd/system/containerd.service.d/teco-runtime.conf << EOF
[Service]
Environment="NVC_LDCONFIG_PERMISSIVE=1"
EOF

systemctl daemon-reload
systemctl restart containerd
```

### 方案2：禁用安全模式（不推荐用于生产环境）

```bash
export NVC_INSECURE_MODE=1
```

### 方案3：修改配置文件

确保配置文件中使用宿主机的 ldconfig：

```toml
[nvidia-container-cli]
ldconfig = "@/sbin/ldconfig"
```

`@` 前缀表示使用宿主机的 ldconfig，这是正确的配置。

## 代码修改说明

本次修改包括：

1. **改进权限处理**：为宿主机 ldconfig 添加了额外的 Linux capabilities（CAP_FOWNER, CAP_CHOWN）
2. **添加宽松模式**：通过 `NVC_LDCONFIG_PERMISSIVE` 环境变量控制是否跳过某些安全限制
3. **优化挂载策略**：在使用宿主机 ldconfig 或宽松模式时，避免将关键目录挂载为只读
4. **跳过安全限制**：当使用宿主机 ldconfig 时，可以选择性跳过资源限制、权限降级和系统调用限制
5. **保持必要权限**：改进了 bounding capabilities 的处理，为宿主机 ldconfig 保留必要的权限
6. **增强系统调用支持**：在 seccomp 白名单中添加了 ldconfig 可能需要的系统调用（fchmod, chown, fchown 等）

### 最新修改（针对权限问题）

基于最新的日志分析，进一步优化了权限处理：

- **智能权限管理**：根据是否使用宿主机 ldconfig 来决定权限策略
- **完全跳过限制**：在宽松模式下，完全跳过可能导致权限问题的安全限制
- **保持 root 权限**：当使用宿主机 ldconfig 且以 root 身份运行时，保持必要的权限

## 验证修复

修复后，ldconfig 应该能够正常执行，不再出现权限错误。可以通过以下方式验证：

1. 检查容器启动日志，确认没有 ldconfig 权限错误
2. 在容器内运行 `ldconfig -p` 验证动态链接库缓存是否正常
3. 测试 TECO 显卡相关功能是否正常工作

## 注意事项

- 宽松模式会降低一定的安全性，建议仅在必要时使用
- 生产环境中建议先在测试环境验证修复效果
- 如果问题仍然存在，可以启用调试日志进行进一步排查
