# TECO-ML 编译错误修复指南

## 问题描述

在运行 `bash run.sh` 编译时遇到以下错误：

```
error: Macro %_version has empty body
make: *** [Makefile:55: rpm] Error 1
```

## 根本原因

1. **版本信息传递问题**：在Docker构建过程中，`LIB_VERSION`和`OS_VERSION`环境变量没有正确传递到容器内部
2. **RPM构建失败**：rpmbuild命令中的`%_version`宏为空，导致构建失败
3. **Docker构建参数缺失**：Dockerfile中缺少必要的ARG声明来接收版本信息

## 修复内容

### 1. 修改了所有Dockerfile文件

**修改的文件：**
- `mk/Dockerfile.anolis`
- `mk/Dockerfile.ubuntu` 
- `mk/Dockerfile.centos`
- `mk/Dockerfile.openeuler`

**修改内容：**
```dockerfile
ARG LIB_NAME
ARG LIB_VERSION
ARG OS_VERSION
ENV LIB_NAME ${LIB_NAME}
ENV LIB_VERSION ${LIB_VERSION}
ENV OS_VERSION ${OS_VERSION}
```

### 2. 修改了docker.mk构建脚本

**修改内容：**
在docker build命令中添加了版本信息传递：
```makefile
--build-arg LIB_NAME="$(LIB_NAME)" \
--build-arg LIB_VERSION="$(LIB_VERSION)" \
--build-arg OS_VERSION="$(OS)$(VERSION)" \
```

### 3. 改进了Makefile的rpm目标

**修改内容：**
- 添加了版本信息检查
- 增加了调试输出
- 确保在版本信息为空时及时报错

```makefile
@echo "Building RPM with LIB_VERSION=$(LIB_VERSION), OS_VERSION=$(OS_VERSION)"
@if [ -z "$(LIB_VERSION)" ]; then echo "Error: LIB_VERSION is empty"; exit 1; fi
@if [ -z "$(OS_VERSION)" ]; then echo "Error: OS_VERSION is empty"; exit 1; fi
```

## 验证修复

修复后，重新运行编译命令：

```bash
cd teco-ml
bash run.sh
```

**预期结果：**
1. 不再出现 `%_version has empty body` 错误
2. RPM包能够正常构建
3. 构建产物正确输出到 `../BSP` 目录

## 调试信息

如果仍然遇到问题，可以通过以下方式获取调试信息：

1. **检查版本信息：**
   ```bash
   cd teco-ml
   make -n rpm
   ```

2. **手动构建测试：**
   ```bash
   cd teco-ml
   make cmds
   make rpm
   ```

3. **检查环境变量：**
   ```bash
   echo "LIB_VERSION: $LIB_VERSION"
   echo "OS_VERSION: $OS_VERSION"
   ```

## 注意事项

1. 确保在项目根目录有有效的git标签
2. 确保系统能够正确识别操作系统信息
3. 如果使用自定义版本，请手动设置相关环境变量

## 相关文件

- `teco-ml/Makefile` - 主构建文件
- `teco-ml/mk/docker.mk` - Docker构建脚本
- `teco-ml/mk/versions.mk` - 版本信息定义
- `teco-ml/mk/Dockerfile.*` - 各平台的Docker构建文件
- `teco-ml/run.sh` - 构建脚本
