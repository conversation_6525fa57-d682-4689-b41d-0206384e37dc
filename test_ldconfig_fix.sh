#!/bin/bash

# TECO Container Runtime - ldconfig 权限问题测试脚本

set -e

echo "=== TECO Container Runtime ldconfig 权限问题测试 ==="
echo

# 检查是否有必要的权限
if [[ $EUID -ne 0 ]]; then
   echo "错误：此脚本需要 root 权限运行"
   exit 1
fi

# 检查 teco-container-runtime 是否存在
if ! command -v /usr/bin/teco-container-runtime &> /dev/null; then
    echo "错误：找不到 /usr/bin/teco-container-runtime"
    exit 1
fi

# 检查 nerdctl 是否存在
if ! command -v /opt/kube/bin/containerd-bin/nerdctl &> /dev/null; then
    echo "错误：找不到 /opt/kube/bin/containerd-bin/nerdctl"
    exit 1
fi

echo "1. 测试不使用宽松模式（可能失败）..."
echo "运行命令：nerdctl run --runtime=/usr/bin/teco-container-runtime -e TECO_VISIBLE_DEVICES=all registry.tecorigin.io:5443/loong64/anolisos:v8.9 echo 'Test without permissive mode'"

if timeout 30 /opt/kube/bin/containerd-bin/nerdctl run --rm \
    --network=host --namespace=k8s.io \
    --security-opt seccomp=unconfined \
    --runtime=/usr/bin/teco-container-runtime \
    -e TECO_VISIBLE_DEVICES=all \
    registry.tecorigin.io:5443/loong64/anolisos:v8.9 \
    echo "Test without permissive mode" 2>/tmp/test_normal.log; then
    echo "✓ 正常模式测试成功"
else
    echo "✗ 正常模式测试失败（预期结果）"
    echo "错误日志："
    tail -10 /var/log/teco-container-toolkit.log 2>/dev/null || echo "无法读取日志文件"
fi

echo
echo "2. 测试使用宽松模式..."
echo "运行命令：NVC_LDCONFIG_PERMISSIVE=1 nerdctl run --runtime=/usr/bin/teco-container-runtime -e TECO_VISIBLE_DEVICES=all registry.tecorigin.io:5443/loong64/anolisos:v8.9 echo 'Test with permissive mode'"

export NVC_LDCONFIG_PERMISSIVE=1

if timeout 30 /opt/kube/bin/containerd-bin/nerdctl run --rm \
    --network=host --namespace=k8s.io \
    --security-opt seccomp=unconfined \
    --runtime=/usr/bin/teco-container-runtime \
    -e TECO_VISIBLE_DEVICES=all \
    registry.tecorigin.io:5443/loong64/anolisos:v8.9 \
    echo "Test with permissive mode" 2>/tmp/test_permissive.log; then
    echo "✓ 宽松模式测试成功！"
    echo "修复生效，ldconfig 权限问题已解决"
else
    echo "✗ 宽松模式测试失败"
    echo "错误日志："
    tail -10 /var/log/teco-container-toolkit.log 2>/dev/null || echo "无法读取日志文件"
    exit 1
fi

echo
echo "3. 测试 TECO 设备可见性..."
if timeout 30 /opt/kube/bin/containerd-bin/nerdctl run --rm \
    --network=host --namespace=k8s.io \
    --security-opt seccomp=unconfined \
    --runtime=/usr/bin/teco-container-runtime \
    -e TECO_VISIBLE_DEVICES=all \
    registry.tecorigin.io:5443/loong64/anolisos:v8.9 \
    ls -la /dev/tcaicard* 2>/dev/null; then
    echo "✓ TECO 设备挂载成功"
else
    echo "✗ TECO 设备挂载失败或设备不存在"
fi

echo
echo "4. 检查 ldconfig 缓存..."
if timeout 30 /opt/kube/bin/containerd-bin/nerdctl run --rm \
    --network=host --namespace=k8s.io \
    --security-opt seccomp=unconfined \
    --runtime=/usr/bin/teco-container-runtime \
    -e TECO_VISIBLE_DEVICES=all \
    registry.tecorigin.io:5443/loong64/anolisos:v8.9 \
    ldconfig -p | grep -E "(teco|sddev)" 2>/dev/null; then
    echo "✓ TECO 相关库已正确加载到 ldconfig 缓存"
else
    echo "! TECO 相关库未在 ldconfig 缓存中找到（可能正常，取决于容器镜像）"
fi

echo
echo "=== 测试完成 ==="
echo "如果宽松模式测试成功，说明修复生效。"
echo "建议在生产环境中设置环境变量 NVC_LDCONFIG_PERMISSIVE=1"

unset NVC_LDCONFIG_PERMISSIVE
