# Tecorigin 容器运行时

tecorigin container runtime toolkit

## 一、项目架构
由三个核心模块构成的容器技术生态：
```
├── libteco-container        # 核心容器运行时库（C语言实现）
├── teco-container-toolkit   # 容器工具集（Go语言实现）
└── teco-ml                  # 太初卡管理库（C语言实现）
```

## 二、核心模块解析

### 1. libteco-container（C语言核心）
作为底层容器运行时引擎，提供：
- **硬件抽象层**：通过`driver.c/h`实现NVIDIA GPU设备驱动交互
- **容器生命周期管理**：
  - `cgroup.c/h` 实现cgroup资源控制
  - `nvc_container.c` 管理容器创建/销毁流程
- **CLI接口**：包含configure/dsl/info等子命令（cli/*.c）
- **扩展机制**：
  - `rpc.c/h` 提供远程过程调用支持
  - `nvcgo.c/h` 实现Go语言绑定

### 2. teco-container-toolkit（Go工具集）
构建在libteco-container之上的应用层工具：
- **运行时组件**：
  - `nvidia-container-runtime`：符合OCI标准的容器运行时
  - `nvidia-container-runtime-hook`：容器生命周期钩子系统
- **核心功能模块**：
  - `discover/`：设备发现系统（GPU/GDS/InfiniBand）
  - `modifier/`：容器配置修改器（CDI/CSV生成）
  - `oci/`：实现OCI容器镜像规范处理
- **配置管理**：
  - 支持Docker/CRI-O等主流容器环境
  - 提供TOML配置格式支持

### 3. teco-ml（太初卡管理）
负责对接管理的模块：
- **资源管理层**：
  - `teco_ml.c/h` 实现GPU资源动态分配
  - 集成NVML（NVIDIA Management Library）进行GPU状态监控

- **性能优化**：
  - 容器间GPU资源共享机制

## 三、关键技术实现

### 1. 跨语言协作架构
采用C/Go混合编程模式：
```
+---------------------+
|  Go工具集           |
|  (toolkit)          |
+----------+----------+
           |
+----------v----------+
|  C核心库            |
|  (libteco-container)|
+----------+----------+
           |
+----------v----------+
|  硬件驱动接口       |
+---------------------+
```

### 2. 构建系统设计
通过mk.sh统一协调各模块构建：
```bash
#!/bin/bash
cd libteco-container && ./run.sh  # 构建核心库
cd teco-container-toolkit && ./run.sh  # 构建工具集
cd teco-ml && ./run.sh           # 构建ML模块
```

### 3. 容器集成方案
- **运行时集成**：通过`nvidia-container-runtime`实现runc兼容接口
- **配置热加载**：使用钩子机制在容器启动时动态注入GPU配置
- **多版本支持**：通过`runtime_factory.go`实现不同运行时版本的兼容

## 四、开发与部署

### 1. 开发环境要求
- 编译工具链：
  - GCC 9+ 或 Clang 12+
  - Go 1.16+
- 依赖库：
  - NVIDIA Driver >= 450.80.02
  - libnvidia-container-tools

### 2. 构建流程
```bash
# 全量构建
make -C libteco-container
make -C teco-container-toolkit
make -C teco-ml

# 单独安装某个模块
make -C libteco-container install
```

### 3. 运行时依赖
- 内核特性：
  - cgroups v1/v2 支持
  - Namespace隔离机制
- 容器环境：
  - Docker >= 19.03
  - containerd >= 1.4

## 五、项目特点
1. **硬件加速优先**：深度优化GPU资源在容器中的利用率
2. **模块化设计**：各组件松耦合，便于独立升级维护
3. **企业级特性**：
   - 支持大规模GPU集群管理
   - 提供细粒度资源配额控制
4. **云原生集成**：完全兼容Kubernetes CSI/CDI规范

该代码库构成了完整的容器GPU解决方案，从底层驱动抽象到高层工具链均具备完整实现，适合需要深度定制容器运行时的企业使用。