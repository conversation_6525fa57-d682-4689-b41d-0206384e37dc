#*************************************************************************
#*              Copyright (c) 2023, Tecorigin
#*                     All rights reserved
#*************************************************************************/

ifeq ($(strip $(MAKECMDGOALS)),)
$(error "No target was specified")
endif

DIST_DIR ?= $(CURDIR)/dist

include $(CURDIR)/mk/common.mk
include $(CURDIR)/mk/docker.mk

include $(CURDIR)/mk/versions.mk 
MAJOR := $(shell echo $(LIB_VERSION) | cut -d. -f1)
MINOR := $(shell echo $(LIB_VERSION) | cut -d. -f2)
PATCH := $(shell echo $(LIB_VERSION) | cut -d. -f3 | grep -oE '^[0-9]+')
VERSION := $(MAJOR).$(MINOR).$(PATCH)

ARCH := $(shell uname -m)

LIB_NAME    ?= libteco-ml2
LIB_SHARED  := $(LIB_NAME).so.$(VERSION)

LIB_SRCS := $(CURDIR)/src/teco_ml.c

CFLAGS   += -fPIC -shared -W -Wall -rdynamic -Wno-unused-function -Wno-unused-parameter -std=gnu99

cmds:
	$(CC) $(CFLAGS) -o $(LIB_SHARED) $(LIB_SRCS)
	ln -sf $(LIB_SHARED) $(LIB_NAME).so
install:
	$(INSTALL) -d -m 755 $(DESTDIR)/etc/docker
	$(INSTALL) -d -m 755 $(DESTDIR)/etc/containerd
	$(INSTALL) -d -m 755 $(DESTDIR)/etc/ld.so.conf.d
	$(INSTALL) -d -m 755 $(DESTDIR)//usr/lib/
	$(INSTALL) -d -m 755 $(DESTDIR)/lib/systemd/system
	$(INSTALL) -m 655 $(DIST_DIR)/$(LIB_SHARED) $(DESTDIR)/usr/lib
	$(INSTALL) -m 655 $(DIST_DIR)/$(LIB_NAME).so $(DESTDIR)/usr/lib
	$(INSTALL) -m 655 $(DIST_DIR)/teco-daemon.json $(DESTDIR)/etc/docker
	$(INSTALL) -m 655 $(DIST_DIR)/teco-config.toml $(DESTDIR)/etc/containerd
	$(INSTALL) -m 655 $(DIST_DIR)/sdaa.conf        $(DESTDIR)/etc/ld.so.conf.d
	$(INSTALL) -m 655 $(DIST_DIR)/teco-docker.service $(DIST_DIR)/teco-containerd.service $(DESTDIR)/lib/systemd/system

deb: DESTDIR:=$(DIST_DIR)/$(LIB_NAME)_$(VERSION)_$(ARCH)
deb: install
	$(CP) -T $(DIST_DIR)/pkg/deb $(DESTDIR)/DEBIAN
	dpkg-deb -b $(DESTDIR) libteco-ml2_$(LIB_VERSION)-1.$(OS_VERSION)_amd64.deb

rpm: DESTDIR:=$(DIST_DIR)/$(LIB_NAME)-$(OS_VERSION)-$(LIB_VERSION)
rpm: install
	@echo "Building RPM with LIB_VERSION=$(LIB_VERSION), OS_VERSION=$(OS_VERSION)"
	@if [ -z "$(LIB_VERSION)" ]; then echo "Error: LIB_VERSION is empty"; exit 1; fi
	@if [ -z "$(OS_VERSION)" ]; then echo "Error: OS_VERSION is empty"; exit 1; fi
	mkdir -p ~/rpmbuild/{BUILD,RPMS,SOURCES,SPECS,SRPMS}
	cd $(DIST_DIR) && tar czf ~/rpmbuild/SOURCES/$(LIB_NAME)-$(OS_VERSION)-$(LIB_VERSION).tar.gz $(LIB_NAME)-$(OS_VERSION)-$(LIB_VERSION)
	rpmbuild -bb pkg/rpm/SPECS/teco-ml2-container.spec -D "_name $(LIB_NAME)" -D "platform $(OS_VERSION)" -D "_version $(LIB_VERSION)"
	cp ~/rpmbuild/RPMS/$(ARCH)/*.rpm $(DIST_DIR)
	rm -rf ~/rpmbuild

