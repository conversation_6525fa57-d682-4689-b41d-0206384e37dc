ARG BASEIMAGE
FROM ${BASEIMAGE}

SHELL ["/bin/bash", "-c"]

ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && apt-get install -y --no-install-recommends \
        apt-utils \
        build-essential \
        bzip2 \
        ca-certificates \
        curl \
        devscripts \
        git \
        tree \
        libcap-dev \
        lsb-release \
        pkg-config \
        xz-utils && \
    rm -rf /var/lib/apt/lists/*


ARG LIB_NAME
ARG LIB_VERSION
ARG OS_VERSION
ENV LIB_NAME ${LIB_NAME}
ENV LIB_VERSION ${LIB_VERSION}
ENV OS_VERSION ${OS_VERSION}
WORKDIR /tmp/${LIB_NAME}
COPY . .

RUN make cmds

ENV DIST_DIR=/tmp/${LIB_NAME}
RUN mkdir -p /dist


CMD bash -c " \
        make deb; \
        mv *.deb /dist; \
     "
