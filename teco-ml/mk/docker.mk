
DOCKER       ?= docker
LIB_NAME     ?= libteco-ml2
PLATFORM     ?= $(shell uname -m)

DIST_DIR     ?= $(CURDIR)/dist
MAKE_DIR     ?= $(CURDIR)/mk
REVISION     ?= $(shell git rev-parse HEAD)

# Supported OSs by architecture
AMD64_TARGETS := ubuntu22.04 ubuntu20.04 ubuntu18.04
X86_64_TARGETS := centos7
SW64_TARGETS := openeuler22.03
LOONGARCH_TARGETS := anolis8.9

# Define top-level build targets
docker%: SHELL:=/bin/bash

# Native targets (for tab completion by OS name only on native platform)
ifeq ($(PLATFORM),x86_64)
NATIVE_TARGETS := $(AMD64_TARGETS) $(X86_64_TARGETS)
$(AMD64_TARGETS): %: %-amd64
$(X86_64_TARGETS): %: %-x86_64
else ifeq ($(PLATFORM),sw_64)
NATIVE_TARGETS := $(SW64_TARGETS)
$(SW64_TARGETS): %: %-sw_64
else ifeq ($(PLATFORM),loongarch64)
NATIVE_TARGETS := $(LOONGARCH_TARGETS)
$(LOONGARCH_TARGETS): %: %-loongarch64
endif
docker-native: $(NATIVE_TARGETS)

# amd64 targets
AMD64_TARGETS := $(patsubst %, %-amd64, $(AMD64_TARGETS))
$(AMD64_TARGETS): ARCH := amd64
$(AMD64_TARGETS): %: --%
docker-amd64: $(AMD64_TARGETS)

# x86_64 targets
X86_64_TARGETS := $(patsubst %, %-x86_64, $(X86_64_TARGETS))
$(X86_64_TARGETS): ARCH := x86_64
$(X86_64_TARGETS): %: --%
docker-x86_64: $(X86_64_TARGETS)

# sw_64 targets
SW64_TARGETS := $(patsubst %, %-sw_64, $(SW64_TARGETS))
$(SW64_TARGETS): ARCH := sw_64
$(SW64_TARGETS): %: --%
docker-sw_64: $(SW64_TARGETS)

# loongarch64 targets
LOONGARCH_TARGETS := $(patsubst %, %-loongarch64, $(LOONGARCH_TARGETS))
$(LOONGARCH_TARGETS): ARCH := loongarch64
$(LOONGARCH_TARGETS): %: --%
docker-loongarch64: $(LOONGARCH_TARGETS)

# docker target to build for all os/arch combinations
docker-all: $(AMD64_TARGETS) $(X86_64_TARGETS) $(SW64_TARGETS) $(LOONGARCH_TARGETS)

# Default variables for all private '--' targets below.
# One private target is defined for each OS we support.
--%: CFLAGS :=
--%: LDLIBS :=
--%: TARGET_PLATFORM = $(*)
--%: VERSION = $(patsubst $(OS)%-$(ARCH),%,$(TARGET_PLATFORM))
--%: BASEIMAGE = $(OS):$(VERSION)
--%: BUILDIMAGE = nvidia/$(LIB_NAME)/$(OS)$(VERSION)-$(ARCH)
--%: DOCKERFILE = $(MAKE_DIR)/Dockerfile.$(OS)
--%: ARTIFACTS_DIR = $(DIST_DIR)/$(OS)$(VERSION)/$(ARCH)
--%: docker-build-%
	@

# Define verify targets to run a minimal sanity check that everything has built
# and runs correctly for a given OS on amd64/x86_64. Requires a working NVIDIA
# driver installation on a native amd64/x86_64 machine.
$(patsubst %, %-verify, $(AMD64_TARGETS)): ARCH := amd64
$(patsubst %, %-verify, $(AMD64_TARGETS)): %-verify: --verify-%
$(patsubst %, %-verify, $(X86_64_TARGETS)): ARCH := x86_64
$(patsubst %, %-verify, $(X86_64_TARGETS)): %-verify: --verify-%
docker-amd64-verify: $(patsubst %, %-verify, $(AMD64_TARGETS)) \
                     $(patsubst %, %-verify, $(X86_64_TARGETS))

--verify-%: docker-verify-%
	@

# private OS targets with defaults
--ubuntu%: OS := ubuntu

# private centos target with overrides
--centos%: OS := centos

# private openeuler target
--openeuler%: OS := openeuler
--openeuler%: BASEIMAGE = jfrog.tecorigin.net/tecotp-docker/release/openeuler22.03/sw_64/full:0.10.0
--openeuler%: ARTIFACTS_DIR = $(DIST_DIR)/$(OS)$(VERSION)/$(ARCH)

# private anolis target
--anolis%: OS := anolis
--anolis%: BASEIMAGE = jfrog.tecorigin.net/tecotp-docker/release/anolisos8.9/loongarch64/basic:v0.0.3
--anolis%: ARTIFACTS_DIR = $(DIST_DIR)/$(OS)$(VERSION)/$(ARCH)

docker-build-%: $(ARTIFACTS_DIR)
	@echo "Building for $(TARGET_PLATFORM)"
	$(DOCKER) pull $(BASEIMAGE)
	DOCKER_BUILDKIT=1 \
	$(DOCKER) build \
	    --progress=plain \
	    --build-arg BASEIMAGE="$(BASEIMAGE)" \
	    --build-arg LIB_NAME="$(LIB_NAME)" \
	    --build-arg LIB_VERSION="$(LIB_VERSION)" \
	    --build-arg OS_VERSION="$(OS)$(VERSION)" \
	    --build-arg OS_ARCH="$(ARCH)" \
	    --build-arg CFLAGS="$(CFLAGS)" \
	    --build-arg LDLIBS="$(LDLIBS)" \
	    --build-arg REVISION="$(REVISION)" \
	    --build-arg SWVERSN="$(SWVERSN)" \
	    $(EXTRA_BUILD_ARGS) \
	    --tag $(BUILDIMAGE) \
	    --file $(DOCKERFILE) .
	mkdir -p $(ARTIFACTS_DIR)
	$(DOCKER) run \
	    -e TAG \
	    -v $(ARTIFACTS_DIR):/dist \
	    $(BUILDIMAGE)

docker-verify-%: %
	@echo "Verifying for $(TARGET_PLATFORM)"
	$(DOCKER) run \
	    --privileged \
	    --runtime=nvidia  \
	    -e NVIDIA_VISIBLE_DEVICES=all \
	    --rm $(BUILDIMAGE) \
	    bash -c "make install; LD_LIBRARY_PATH=/usr/local/lib/  teco-container-cli -k -d /dev/tty info"

docker-clean:
	IMAGES=$$(docker images "nvidia/$(LIB_NAME)/*" --format="{{.ID}}"); \
	if [ "$${IMAGES}" != "" ]; then \
	    docker rmi -f $${IMAGES}; \
	fi

$(ARTIFACTS_DIR):
	echo "*********************************"
	echo $(ARTIFACTS_DIR)
	echo $(@)
	mkdir -p $(@)
