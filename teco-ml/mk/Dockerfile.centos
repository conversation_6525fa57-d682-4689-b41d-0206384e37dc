ARG BASEIMAGE
FROM ${BASEIMAGE}

RUN yum install -y \
        createrepo \
        gcc \
        git \
        make \
        libcap-devel \
        rpm-build \
        rpmlint \
        which && \
    rm -rf /var/cache/yum/*

ARG LIB_NAME
ARG LIB_VERSION
ARG OS_VERSION
ENV LIB_NAME ${LIB_NAME}
ENV LIB_VERSION ${LIB_VERSION}
ENV OS_VERSION ${OS_VERSION}
WORKDIR /tmp/${LIB_NAME}
COPY . .

RUN make cmds

ENV DIST_DIR=/tmp/${LIB_NAME}
RUN mkdir -p /dist

CMD bash -c " \
     make rpm; \
     mv *.rpm /dist; \
    "
