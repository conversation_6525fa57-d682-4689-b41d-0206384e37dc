// Code generated by "stringer -type AttachType -trimprefix Attach"; DO NOT EDIT.

package ebpf

import "strconv"

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[AttachNone-0]
	_ = x[AttachCGroupInetIngress-0]
	_ = x[AttachCGroupInetEgress-1]
	_ = x[AttachCGroupInetSockCreate-2]
	_ = x[AttachCGroupSockOps-3]
	_ = x[AttachSkSKBStreamParser-4]
	_ = x[AttachSkSKBStreamVerdict-5]
	_ = x[AttachCGroupDevice-6]
	_ = x[AttachSkMsgVerdict-7]
	_ = x[AttachCGroupInet4Bind-8]
	_ = x[AttachCGroupInet6Bind-9]
	_ = x[AttachCGroupInet4Connect-10]
	_ = x[AttachCGroupInet6Connect-11]
	_ = x[AttachCGroupInet4PostBind-12]
	_ = x[AttachCGroupInet6PostBind-13]
	_ = x[AttachCGroupUDP4Sendmsg-14]
	_ = x[AttachCGroupUDP6Sendmsg-15]
	_ = x[AttachLircMode2-16]
	_ = x[AttachFlowDissector-17]
	_ = x[AttachCGroupSysctl-18]
	_ = x[AttachCGroupUDP4Recvmsg-19]
	_ = x[AttachCGroupUDP6Recvmsg-20]
	_ = x[AttachCGroupGetsockopt-21]
	_ = x[AttachCGroupSetsockopt-22]
	_ = x[AttachTraceRawTp-23]
	_ = x[AttachTraceFEntry-24]
	_ = x[AttachTraceFExit-25]
	_ = x[AttachModifyReturn-26]
	_ = x[AttachLSMMac-27]
	_ = x[AttachTraceIter-28]
	_ = x[AttachCgroupInet4GetPeername-29]
	_ = x[AttachCgroupInet6GetPeername-30]
	_ = x[AttachCgroupInet4GetSockname-31]
	_ = x[AttachCgroupInet6GetSockname-32]
	_ = x[AttachXDPDevMap-33]
	_ = x[AttachCgroupInetSockRelease-34]
	_ = x[AttachXDPCPUMap-35]
	_ = x[AttachSkLookup-36]
	_ = x[AttachXDP-37]
	_ = x[AttachSkSKBVerdict-38]
	_ = x[AttachSkReuseportSelect-39]
	_ = x[AttachSkReuseportSelectOrMigrate-40]
	_ = x[AttachPerfEvent-41]
}

const _AttachType_name = "NoneCGroupInetEgressCGroupInetSockCreateCGroupSockOpsSkSKBStreamParserSkSKBStreamVerdictCGroupDeviceSkMsgVerdictCGroupInet4BindCGroupInet6BindCGroupInet4ConnectCGroupInet6ConnectCGroupInet4PostBindCGroupInet6PostBindCGroupUDP4SendmsgCGroupUDP6SendmsgLircMode2FlowDissectorCGroupSysctlCGroupUDP4RecvmsgCGroupUDP6RecvmsgCGroupGetsockoptCGroupSetsockoptTraceRawTpTraceFEntryTraceFExitModifyReturnLSMMacTraceIterCgroupInet4GetPeernameCgroupInet6GetPeernameCgroupInet4GetSocknameCgroupInet6GetSocknameXDPDevMapCgroupInetSockReleaseXDPCPUMapSkLookupXDPSkSKBVerdictSkReuseportSelectSkReuseportSelectOrMigratePerfEvent"

var _AttachType_index = [...]uint16{0, 4, 20, 40, 53, 70, 88, 100, 112, 127, 142, 160, 178, 197, 216, 233, 250, 259, 272, 284, 301, 318, 334, 350, 360, 371, 381, 393, 399, 408, 430, 452, 474, 496, 505, 526, 535, 543, 546, 558, 575, 601, 610}

func (i AttachType) String() string {
	if i >= AttachType(len(_AttachType_index)-1) {
		return "AttachType(" + strconv.FormatInt(int64(i), 10) + ")"
	}
	return _AttachType_name[_AttachType_index[i]:_AttachType_index[i+1]]
}
