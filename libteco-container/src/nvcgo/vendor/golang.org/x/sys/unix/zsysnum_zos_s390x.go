// Copyright 2020 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

//go:build zos && s390x
// +build zos,s390x

package unix

// TODO: auto-generate.

const (
	SYS_ACOSD128                        = 0xB80
	SYS_ACOSD32                         = 0xB7E
	SYS_ACOSD64                         = 0xB7F
	SYS_ACOSHD128                       = 0xB83
	SYS_ACOSHD32                        = 0xB81
	SYS_ACOSHD64                        = 0xB82
	SYS_AIO_FSYNC                       = 0xC69
	SYS_ASCTIME                         = 0x0AE
	SYS_ASCTIME64                       = 0xCD7
	SYS_ASCTIME64_R                     = 0xCD8
	SYS_ASIND128                        = 0xB86
	SYS_ASIND32                         = 0xB84
	SYS_ASIND64                         = 0xB85
	SYS_ASINHD128                       = 0xB89
	SYS_ASINHD32                        = 0xB87
	SYS_ASINHD64                        = 0xB88
	SYS_ATAN2D128                       = 0xB8F
	SYS_ATAN2D32                        = 0xB8D
	SYS_ATAN2D64                        = 0xB8E
	SYS_ATAND128                        = 0xB8C
	SYS_ATAND32                         = 0xB8A
	SYS_ATAND64                         = 0xB8B
	SYS_ATANHD128                       = 0xB92
	SYS_ATANHD32                        = 0xB90
	SYS_ATANHD64                        = 0xB91
	SYS_BIND2ADDRSEL                    = 0xD59
	SYS_C16RTOMB                        = 0xD40
	SYS_C32RTOMB                        = 0xD41
	SYS_CBRTD128                        = 0xB95
	SYS_CBRTD32                         = 0xB93
	SYS_CBRTD64                         = 0xB94
	SYS_CEILD128                        = 0xB98
	SYS_CEILD32                         = 0xB96
	SYS_CEILD64                         = 0xB97
	SYS_CLEARENV                        = 0x0C9
	SYS_CLEARERR_UNLOCKED               = 0xCA1
	SYS_CLOCK                           = 0x0AA
	SYS_CLOGL                           = 0xA00
	SYS_CLRMEMF                         = 0x0BD
	SYS_CONJ                            = 0xA03
	SYS_CONJF                           = 0xA06
	SYS_CONJL                           = 0xA09
	SYS_COPYSIGND128                    = 0xB9E
	SYS_COPYSIGND32                     = 0xB9C
	SYS_COPYSIGND64                     = 0xB9D
	SYS_COSD128                         = 0xBA1
	SYS_COSD32                          = 0xB9F
	SYS_COSD64                          = 0xBA0
	SYS_COSHD128                        = 0xBA4
	SYS_COSHD32                         = 0xBA2
	SYS_COSHD64                         = 0xBA3
	SYS_CPOW                            = 0xA0C
	SYS_CPOWF                           = 0xA0F
	SYS_CPOWL                           = 0xA12
	SYS_CPROJ                           = 0xA15
	SYS_CPROJF                          = 0xA18
	SYS_CPROJL                          = 0xA1B
	SYS_CREAL                           = 0xA1E
	SYS_CREALF                          = 0xA21
	SYS_CREALL                          = 0xA24
	SYS_CSIN                            = 0xA27
	SYS_CSINF                           = 0xA2A
	SYS_CSINH                           = 0xA30
	SYS_CSINHF                          = 0xA33
	SYS_CSINHL                          = 0xA36
	SYS_CSINL                           = 0xA2D
	SYS_CSNAP                           = 0x0C5
	SYS_CSQRT                           = 0xA39
	SYS_CSQRTF                          = 0xA3C
	SYS_CSQRTL                          = 0xA3F
	SYS_CTAN                            = 0xA42
	SYS_CTANF                           = 0xA45
	SYS_CTANH                           = 0xA4B
	SYS_CTANHF                          = 0xA4E
	SYS_CTANHL                          = 0xA51
	SYS_CTANL                           = 0xA48
	SYS_CTIME                           = 0x0AB
	SYS_CTIME64                         = 0xCD9
	SYS_CTIME64_R                       = 0xCDA
	SYS_CTRACE                          = 0x0C6
	SYS_DIFFTIME                        = 0x0A7
	SYS_DIFFTIME64                      = 0xCDB
	SYS_DLADDR                          = 0xC82
	SYS_DYNALLOC                        = 0x0C3
	SYS_DYNFREE                         = 0x0C2
	SYS_ERFCD128                        = 0xBAA
	SYS_ERFCD32                         = 0xBA8
	SYS_ERFCD64                         = 0xBA9
	SYS_ERFD128                         = 0xBA7
	SYS_ERFD32                          = 0xBA5
	SYS_ERFD64                          = 0xBA6
	SYS_EXP2D128                        = 0xBB0
	SYS_EXP2D32                         = 0xBAE
	SYS_EXP2D64                         = 0xBAF
	SYS_EXPD128                         = 0xBAD
	SYS_EXPD32                          = 0xBAB
	SYS_EXPD64                          = 0xBAC
	SYS_EXPM1D128                       = 0xBB3
	SYS_EXPM1D32                        = 0xBB1
	SYS_EXPM1D64                        = 0xBB2
	SYS_FABSD128                        = 0xBB6
	SYS_FABSD32                         = 0xBB4
	SYS_FABSD64                         = 0xBB5
	SYS_FDELREC_UNLOCKED                = 0xCA2
	SYS_FDIMD128                        = 0xBB9
	SYS_FDIMD32                         = 0xBB7
	SYS_FDIMD64                         = 0xBB8
	SYS_FDOPEN_UNLOCKED                 = 0xCFC
	SYS_FECLEAREXCEPT                   = 0xAEA
	SYS_FEGETENV                        = 0xAEB
	SYS_FEGETEXCEPTFLAG                 = 0xAEC
	SYS_FEGETROUND                      = 0xAED
	SYS_FEHOLDEXCEPT                    = 0xAEE
	SYS_FEOF_UNLOCKED                   = 0xCA3
	SYS_FERAISEEXCEPT                   = 0xAEF
	SYS_FERROR_UNLOCKED                 = 0xCA4
	SYS_FESETENV                        = 0xAF0
	SYS_FESETEXCEPTFLAG                 = 0xAF1
	SYS_FESETROUND                      = 0xAF2
	SYS_FETCHEP                         = 0x0BF
	SYS_FETESTEXCEPT                    = 0xAF3
	SYS_FEUPDATEENV                     = 0xAF4
	SYS_FE_DEC_GETROUND                 = 0xBBA
	SYS_FE_DEC_SETROUND                 = 0xBBB
	SYS_FFLUSH_UNLOCKED                 = 0xCA5
	SYS_FGETC_UNLOCKED                  = 0xC80
	SYS_FGETPOS64                       = 0xCEE
	SYS_FGETPOS64_UNLOCKED              = 0xCF4
	SYS_FGETPOS_UNLOCKED                = 0xCA6
	SYS_FGETS_UNLOCKED                  = 0xC7C
	SYS_FGETWC_UNLOCKED                 = 0xCA7
	SYS_FGETWS_UNLOCKED                 = 0xCA8
	SYS_FILENO_UNLOCKED                 = 0xCA9
	SYS_FLDATA                          = 0x0C1
	SYS_FLDATA_UNLOCKED                 = 0xCAA
	SYS_FLOCATE_UNLOCKED                = 0xCAB
	SYS_FLOORD128                       = 0xBBE
	SYS_FLOORD32                        = 0xBBC
	SYS_FLOORD64                        = 0xBBD
	SYS_FMA                             = 0xA63
	SYS_FMAD128                         = 0xBC1
	SYS_FMAD32                          = 0xBBF
	SYS_FMAD64                          = 0xBC0
	SYS_FMAF                            = 0xA66
	SYS_FMAL                            = 0xA69
	SYS_FMAX                            = 0xA6C
	SYS_FMAXD128                        = 0xBC4
	SYS_FMAXD32                         = 0xBC2
	SYS_FMAXD64                         = 0xBC3
	SYS_FMAXF                           = 0xA6F
	SYS_FMAXL                           = 0xA72
	SYS_FMIN                            = 0xA75
	SYS_FMIND128                        = 0xBC7
	SYS_FMIND32                         = 0xBC5
	SYS_FMIND64                         = 0xBC6
	SYS_FMINF                           = 0xA78
	SYS_FMINL                           = 0xA7B
	SYS_FMODD128                        = 0xBCA
	SYS_FMODD32                         = 0xBC8
	SYS_FMODD64                         = 0xBC9
	SYS_FOPEN64                         = 0xD49
	SYS_FOPEN64_UNLOCKED                = 0xD4A
	SYS_FOPEN_UNLOCKED                  = 0xCFA
	SYS_FPRINTF_UNLOCKED                = 0xCAC
	SYS_FPUTC_UNLOCKED                  = 0xC81
	SYS_FPUTS_UNLOCKED                  = 0xC7E
	SYS_FPUTWC_UNLOCKED                 = 0xCAD
	SYS_FPUTWS_UNLOCKED                 = 0xCAE
	SYS_FREAD_NOUPDATE                  = 0xCEC
	SYS_FREAD_NOUPDATE_UNLOCKED         = 0xCED
	SYS_FREAD_UNLOCKED                  = 0xC7B
	SYS_FREEIFADDRS                     = 0xCE6
	SYS_FREOPEN64                       = 0xD4B
	SYS_FREOPEN64_UNLOCKED              = 0xD4C
	SYS_FREOPEN_UNLOCKED                = 0xCFB
	SYS_FREXPD128                       = 0xBCE
	SYS_FREXPD32                        = 0xBCC
	SYS_FREXPD64                        = 0xBCD
	SYS_FSCANF_UNLOCKED                 = 0xCAF
	SYS_FSEEK64                         = 0xCEF
	SYS_FSEEK64_UNLOCKED                = 0xCF5
	SYS_FSEEKO64                        = 0xCF0
	SYS_FSEEKO64_UNLOCKED               = 0xCF6
	SYS_FSEEKO_UNLOCKED                 = 0xCB1
	SYS_FSEEK_UNLOCKED                  = 0xCB0
	SYS_FSETPOS64                       = 0xCF1
	SYS_FSETPOS64_UNLOCKED              = 0xCF7
	SYS_FSETPOS_UNLOCKED                = 0xCB3
	SYS_FTELL64                         = 0xCF2
	SYS_FTELL64_UNLOCKED                = 0xCF8
	SYS_FTELLO64                        = 0xCF3
	SYS_FTELLO64_UNLOCKED               = 0xCF9
	SYS_FTELLO_UNLOCKED                 = 0xCB5
	SYS_FTELL_UNLOCKED                  = 0xCB4
	SYS_FUPDATE                         = 0x0B5
	SYS_FUPDATE_UNLOCKED                = 0xCB7
	SYS_FWIDE_UNLOCKED                  = 0xCB8
	SYS_FWPRINTF_UNLOCKED               = 0xCB9
	SYS_FWRITE_UNLOCKED                 = 0xC7A
	SYS_FWSCANF_UNLOCKED                = 0xCBA
	SYS_GETDATE64                       = 0xD4F
	SYS_GETIFADDRS                      = 0xCE7
	SYS_GETIPV4SOURCEFILTER             = 0xC77
	SYS_GETSOURCEFILTER                 = 0xC79
	SYS_GETSYNTX                        = 0x0FD
	SYS_GETS_UNLOCKED                   = 0xC7D
	SYS_GETTIMEOFDAY64                  = 0xD50
	SYS_GETWCHAR_UNLOCKED               = 0xCBC
	SYS_GETWC_UNLOCKED                  = 0xCBB
	SYS_GMTIME                          = 0x0B0
	SYS_GMTIME64                        = 0xCDC
	SYS_GMTIME64_R                      = 0xCDD
	SYS_HYPOTD128                       = 0xBD1
	SYS_HYPOTD32                        = 0xBCF
	SYS_HYPOTD64                        = 0xBD0
	SYS_ILOGBD128                       = 0xBD4
	SYS_ILOGBD32                        = 0xBD2
	SYS_ILOGBD64                        = 0xBD3
	SYS_ILOGBF                          = 0xA7E
	SYS_ILOGBL                          = 0xA81
	SYS_INET6_IS_SRCADDR                = 0xD5A
	SYS_ISBLANK                         = 0x0FE
	SYS_ISWALNUM                        = 0x0FF
	SYS_LDEXPD128                       = 0xBD7
	SYS_LDEXPD32                        = 0xBD5
	SYS_LDEXPD64                        = 0xBD6
	SYS_LGAMMAD128                      = 0xBDA
	SYS_LGAMMAD32                       = 0xBD8
	SYS_LGAMMAD64                       = 0xBD9
	SYS_LIO_LISTIO                      = 0xC6A
	SYS_LLRINT                          = 0xA84
	SYS_LLRINTD128                      = 0xBDD
	SYS_LLRINTD32                       = 0xBDB
	SYS_LLRINTD64                       = 0xBDC
	SYS_LLRINTF                         = 0xA87
	SYS_LLRINTL                         = 0xA8A
	SYS_LLROUND                         = 0xA8D
	SYS_LLROUNDD128                     = 0xBE0
	SYS_LLROUNDD32                      = 0xBDE
	SYS_LLROUNDD64                      = 0xBDF
	SYS_LLROUNDF                        = 0xA90
	SYS_LLROUNDL                        = 0xA93
	SYS_LOCALTIM                        = 0x0B1
	SYS_LOCALTIME                       = 0x0B1
	SYS_LOCALTIME64                     = 0xCDE
	SYS_LOCALTIME64_R                   = 0xCDF
	SYS_LOG10D128                       = 0xBE6
	SYS_LOG10D32                        = 0xBE4
	SYS_LOG10D64                        = 0xBE5
	SYS_LOG1PD128                       = 0xBE9
	SYS_LOG1PD32                        = 0xBE7
	SYS_LOG1PD64                        = 0xBE8
	SYS_LOG2D128                        = 0xBEC
	SYS_LOG2D32                         = 0xBEA
	SYS_LOG2D64                         = 0xBEB
	SYS_LOGBD128                        = 0xBEF
	SYS_LOGBD32                         = 0xBED
	SYS_LOGBD64                         = 0xBEE
	SYS_LOGBF                           = 0xA96
	SYS_LOGBL                           = 0xA99
	SYS_LOGD128                         = 0xBE3
	SYS_LOGD32                          = 0xBE1
	SYS_LOGD64                          = 0xBE2
	SYS_LRINT                           = 0xA9C
	SYS_LRINTD128                       = 0xBF2
	SYS_LRINTD32                        = 0xBF0
	SYS_LRINTD64                        = 0xBF1
	SYS_LRINTF                          = 0xA9F
	SYS_LRINTL                          = 0xAA2
	SYS_LROUNDD128                      = 0xBF5
	SYS_LROUNDD32                       = 0xBF3
	SYS_LROUNDD64                       = 0xBF4
	SYS_LROUNDL                         = 0xAA5
	SYS_MBLEN                           = 0x0AF
	SYS_MBRTOC16                        = 0xD42
	SYS_MBRTOC32                        = 0xD43
	SYS_MEMSET                          = 0x0A3
	SYS_MKTIME                          = 0x0AC
	SYS_MKTIME64                        = 0xCE0
	SYS_MODFD128                        = 0xBF8
	SYS_MODFD32                         = 0xBF6
	SYS_MODFD64                         = 0xBF7
	SYS_NAN                             = 0xAA8
	SYS_NAND128                         = 0xBFB
	SYS_NAND32                          = 0xBF9
	SYS_NAND64                          = 0xBFA
	SYS_NANF                            = 0xAAA
	SYS_NANL                            = 0xAAC
	SYS_NEARBYINT                       = 0xAAE
	SYS_NEARBYINTD128                   = 0xBFE
	SYS_NEARBYINTD32                    = 0xBFC
	SYS_NEARBYINTD64                    = 0xBFD
	SYS_NEARBYINTF                      = 0xAB1
	SYS_NEARBYINTL                      = 0xAB4
	SYS_NEXTAFTERD128                   = 0xC01
	SYS_NEXTAFTERD32                    = 0xBFF
	SYS_NEXTAFTERD64                    = 0xC00
	SYS_NEXTAFTERF                      = 0xAB7
	SYS_NEXTAFTERL                      = 0xABA
	SYS_NEXTTOWARD                      = 0xABD
	SYS_NEXTTOWARDD128                  = 0xC04
	SYS_NEXTTOWARDD32                   = 0xC02
	SYS_NEXTTOWARDD64                   = 0xC03
	SYS_NEXTTOWARDF                     = 0xAC0
	SYS_NEXTTOWARDL                     = 0xAC3
	SYS_NL_LANGINFO                     = 0x0FC
	SYS_PERROR_UNLOCKED                 = 0xCBD
	SYS_POSIX_FALLOCATE                 = 0xCE8
	SYS_POSIX_MEMALIGN                  = 0xCE9
	SYS_POSIX_OPENPT                    = 0xC66
	SYS_POWD128                         = 0xC07
	SYS_POWD32                          = 0xC05
	SYS_POWD64                          = 0xC06
	SYS_PRINTF_UNLOCKED                 = 0xCBE
	SYS_PSELECT                         = 0xC67
	SYS_PTHREAD_ATTR_GETSTACK           = 0xB3E
	SYS_PTHREAD_ATTR_SETSTACK           = 0xB3F
	SYS_PTHREAD_SECURITY_APPLID_NP      = 0xCE4
	SYS_PUTS_UNLOCKED                   = 0xC7F
	SYS_PUTWCHAR_UNLOCKED               = 0xCC0
	SYS_PUTWC_UNLOCKED                  = 0xCBF
	SYS_QUANTEXPD128                    = 0xD46
	SYS_QUANTEXPD32                     = 0xD44
	SYS_QUANTEXPD64                     = 0xD45
	SYS_QUANTIZED128                    = 0xC0A
	SYS_QUANTIZED32                     = 0xC08
	SYS_QUANTIZED64                     = 0xC09
	SYS_REMAINDERD128                   = 0xC0D
	SYS_REMAINDERD32                    = 0xC0B
	SYS_REMAINDERD64                    = 0xC0C
	SYS_RESIZE_ALLOC                    = 0xCEB
	SYS_REWIND_UNLOCKED                 = 0xCC1
	SYS_RINTD128                        = 0xC13
	SYS_RINTD32                         = 0xC11
	SYS_RINTD64                         = 0xC12
	SYS_RINTF                           = 0xACB
	SYS_RINTL                           = 0xACD
	SYS_ROUND                           = 0xACF
	SYS_ROUNDD128                       = 0xC16
	SYS_ROUNDD32                        = 0xC14
	SYS_ROUNDD64                        = 0xC15
	SYS_ROUNDF                          = 0xAD2
	SYS_ROUNDL                          = 0xAD5
	SYS_SAMEQUANTUMD128                 = 0xC19
	SYS_SAMEQUANTUMD32                  = 0xC17
	SYS_SAMEQUANTUMD64                  = 0xC18
	SYS_SCALBLN                         = 0xAD8
	SYS_SCALBLND128                     = 0xC1C
	SYS_SCALBLND32                      = 0xC1A
	SYS_SCALBLND64                      = 0xC1B
	SYS_SCALBLNF                        = 0xADB
	SYS_SCALBLNL                        = 0xADE
	SYS_SCALBND128                      = 0xC1F
	SYS_SCALBND32                       = 0xC1D
	SYS_SCALBND64                       = 0xC1E
	SYS_SCALBNF                         = 0xAE3
	SYS_SCALBNL                         = 0xAE6
	SYS_SCANF_UNLOCKED                  = 0xCC2
	SYS_SCHED_YIELD                     = 0xB32
	SYS_SETENV                          = 0x0C8
	SYS_SETIPV4SOURCEFILTER             = 0xC76
	SYS_SETSOURCEFILTER                 = 0xC78
	SYS_SHM_OPEN                        = 0xC8C
	SYS_SHM_UNLINK                      = 0xC8D
	SYS_SIND128                         = 0xC22
	SYS_SIND32                          = 0xC20
	SYS_SIND64                          = 0xC21
	SYS_SINHD128                        = 0xC25
	SYS_SINHD32                         = 0xC23
	SYS_SINHD64                         = 0xC24
	SYS_SIZEOF_ALLOC                    = 0xCEA
	SYS_SOCKATMARK                      = 0xC68
	SYS_SQRTD128                        = 0xC28
	SYS_SQRTD32                         = 0xC26
	SYS_SQRTD64                         = 0xC27
	SYS_STRCHR                          = 0x0A0
	SYS_STRCSPN                         = 0x0A1
	SYS_STRERROR                        = 0x0A8
	SYS_STRERROR_R                      = 0xB33
	SYS_STRFTIME                        = 0x0B2
	SYS_STRLEN                          = 0x0A9
	SYS_STRPBRK                         = 0x0A2
	SYS_STRSPN                          = 0x0A4
	SYS_STRSTR                          = 0x0A5
	SYS_STRTOD128                       = 0xC2B
	SYS_STRTOD32                        = 0xC29
	SYS_STRTOD64                        = 0xC2A
	SYS_STRTOK                          = 0x0A6
	SYS_TAND128                         = 0xC2E
	SYS_TAND32                          = 0xC2C
	SYS_TAND64                          = 0xC2D
	SYS_TANHD128                        = 0xC31
	SYS_TANHD32                         = 0xC2F
	SYS_TANHD64                         = 0xC30
	SYS_TGAMMAD128                      = 0xC34
	SYS_TGAMMAD32                       = 0xC32
	SYS_TGAMMAD64                       = 0xC33
	SYS_TIME                            = 0x0AD
	SYS_TIME64                          = 0xCE1
	SYS_TMPFILE64                       = 0xD4D
	SYS_TMPFILE64_UNLOCKED              = 0xD4E
	SYS_TMPFILE_UNLOCKED                = 0xCFD
	SYS_TRUNCD128                       = 0xC40
	SYS_TRUNCD32                        = 0xC3E
	SYS_TRUNCD64                        = 0xC3F
	SYS_UNGETC_UNLOCKED                 = 0xCC3
	SYS_UNGETWC_UNLOCKED                = 0xCC4
	SYS_UNSETENV                        = 0xB34
	SYS_VFPRINTF_UNLOCKED               = 0xCC5
	SYS_VFSCANF_UNLOCKED                = 0xCC7
	SYS_VFWPRINTF_UNLOCKED              = 0xCC9
	SYS_VFWSCANF_UNLOCKED               = 0xCCB
	SYS_VPRINTF_UNLOCKED                = 0xCCD
	SYS_VSCANF_UNLOCKED                 = 0xCCF
	SYS_VWPRINTF_UNLOCKED               = 0xCD1
	SYS_VWSCANF_UNLOCKED                = 0xCD3
	SYS_WCSTOD128                       = 0xC43
	SYS_WCSTOD32                        = 0xC41
	SYS_WCSTOD64                        = 0xC42
	SYS_WPRINTF_UNLOCKED                = 0xCD5
	SYS_WSCANF_UNLOCKED                 = 0xCD6
	SYS__FLUSHLBF                       = 0xD68
	SYS__FLUSHLBF_UNLOCKED              = 0xD6F
	SYS___ACOSHF_H                      = 0xA54
	SYS___ACOSHL_H                      = 0xA55
	SYS___ASINHF_H                      = 0xA56
	SYS___ASINHL_H                      = 0xA57
	SYS___ATANPID128                    = 0xC6D
	SYS___ATANPID32                     = 0xC6B
	SYS___ATANPID64                     = 0xC6C
	SYS___CBRTF_H                       = 0xA58
	SYS___CBRTL_H                       = 0xA59
	SYS___CDUMP                         = 0x0C4
	SYS___CLASS                         = 0xAFA
	SYS___CLASS2                        = 0xB99
	SYS___CLASS2D128                    = 0xC99
	SYS___CLASS2D32                     = 0xC97
	SYS___CLASS2D64                     = 0xC98
	SYS___CLASS2F                       = 0xC91
	SYS___CLASS2F_B                     = 0xC93
	SYS___CLASS2F_H                     = 0xC94
	SYS___CLASS2L                       = 0xC92
	SYS___CLASS2L_B                     = 0xC95
	SYS___CLASS2L_H                     = 0xC96
	SYS___CLASS2_B                      = 0xB9A
	SYS___CLASS2_H                      = 0xB9B
	SYS___CLASS_B                       = 0xAFB
	SYS___CLASS_H                       = 0xAFC
	SYS___CLOGL_B                       = 0xA01
	SYS___CLOGL_H                       = 0xA02
	SYS___CLRENV                        = 0x0C9
	SYS___CLRMF                         = 0x0BD
	SYS___CODEPAGE_INFO                 = 0xC64
	SYS___CONJF_B                       = 0xA07
	SYS___CONJF_H                       = 0xA08
	SYS___CONJL_B                       = 0xA0A
	SYS___CONJL_H                       = 0xA0B
	SYS___CONJ_B                        = 0xA04
	SYS___CONJ_H                        = 0xA05
	SYS___COPYSIGN_B                    = 0xA5A
	SYS___COPYSIGN_H                    = 0xAF5
	SYS___COSPID128                     = 0xC70
	SYS___COSPID32                      = 0xC6E
	SYS___COSPID64                      = 0xC6F
	SYS___CPOWF_B                       = 0xA10
	SYS___CPOWF_H                       = 0xA11
	SYS___CPOWL_B                       = 0xA13
	SYS___CPOWL_H                       = 0xA14
	SYS___CPOW_B                        = 0xA0D
	SYS___CPOW_H                        = 0xA0E
	SYS___CPROJF_B                      = 0xA19
	SYS___CPROJF_H                      = 0xA1A
	SYS___CPROJL_B                      = 0xA1C
	SYS___CPROJL_H                      = 0xA1D
	SYS___CPROJ_B                       = 0xA16
	SYS___CPROJ_H                       = 0xA17
	SYS___CREALF_B                      = 0xA22
	SYS___CREALF_H                      = 0xA23
	SYS___CREALL_B                      = 0xA25
	SYS___CREALL_H                      = 0xA26
	SYS___CREAL_B                       = 0xA1F
	SYS___CREAL_H                       = 0xA20
	SYS___CSINF_B                       = 0xA2B
	SYS___CSINF_H                       = 0xA2C
	SYS___CSINHF_B                      = 0xA34
	SYS___CSINHF_H                      = 0xA35
	SYS___CSINHL_B                      = 0xA37
	SYS___CSINHL_H                      = 0xA38
	SYS___CSINH_B                       = 0xA31
	SYS___CSINH_H                       = 0xA32
	SYS___CSINL_B                       = 0xA2E
	SYS___CSINL_H                       = 0xA2F
	SYS___CSIN_B                        = 0xA28
	SYS___CSIN_H                        = 0xA29
	SYS___CSNAP                         = 0x0C5
	SYS___CSQRTF_B                      = 0xA3D
	SYS___CSQRTF_H                      = 0xA3E
	SYS___CSQRTL_B                      = 0xA40
	SYS___CSQRTL_H                      = 0xA41
	SYS___CSQRT_B                       = 0xA3A
	SYS___CSQRT_H                       = 0xA3B
	SYS___CTANF_B                       = 0xA46
	SYS___CTANF_H                       = 0xA47
	SYS___CTANHF_B                      = 0xA4F
	SYS___CTANHF_H                      = 0xA50
	SYS___CTANHL_B                      = 0xA52
	SYS___CTANHL_H                      = 0xA53
	SYS___CTANH_B                       = 0xA4C
	SYS___CTANH_H                       = 0xA4D
	SYS___CTANL_B                       = 0xA49
	SYS___CTANL_H                       = 0xA4A
	SYS___CTAN_B                        = 0xA43
	SYS___CTAN_H                        = 0xA44
	SYS___CTEST                         = 0x0C7
	SYS___CTRACE                        = 0x0C6
	SYS___D1TOP                         = 0xC9B
	SYS___D2TOP                         = 0xC9C
	SYS___D4TOP                         = 0xC9D
	SYS___DYNALL                        = 0x0C3
	SYS___DYNFRE                        = 0x0C2
	SYS___EXP2F_H                       = 0xA5E
	SYS___EXP2L_H                       = 0xA5F
	SYS___EXP2_H                        = 0xA5D
	SYS___EXPM1F_H                      = 0xA5B
	SYS___EXPM1L_H                      = 0xA5C
	SYS___FBUFSIZE                      = 0xD60
	SYS___FLBF                          = 0xD62
	SYS___FLDATA                        = 0x0C1
	SYS___FMAF_B                        = 0xA67
	SYS___FMAF_H                        = 0xA68
	SYS___FMAL_B                        = 0xA6A
	SYS___FMAL_H                        = 0xA6B
	SYS___FMAXF_B                       = 0xA70
	SYS___FMAXF_H                       = 0xA71
	SYS___FMAXL_B                       = 0xA73
	SYS___FMAXL_H                       = 0xA74
	SYS___FMAX_B                        = 0xA6D
	SYS___FMAX_H                        = 0xA6E
	SYS___FMA_B                         = 0xA64
	SYS___FMA_H                         = 0xA65
	SYS___FMINF_B                       = 0xA79
	SYS___FMINF_H                       = 0xA7A
	SYS___FMINL_B                       = 0xA7C
	SYS___FMINL_H                       = 0xA7D
	SYS___FMIN_B                        = 0xA76
	SYS___FMIN_H                        = 0xA77
	SYS___FPENDING                      = 0xD61
	SYS___FPENDING_UNLOCKED             = 0xD6C
	SYS___FPURGE                        = 0xD69
	SYS___FPURGE_UNLOCKED               = 0xD70
	SYS___FP_CAST_D                     = 0xBCB
	SYS___FREADABLE                     = 0xD63
	SYS___FREADAHEAD                    = 0xD6A
	SYS___FREADAHEAD_UNLOCKED           = 0xD71
	SYS___FREADING                      = 0xD65
	SYS___FREADING_UNLOCKED             = 0xD6D
	SYS___FSEEK2                        = 0xB3C
	SYS___FSETERR                       = 0xD6B
	SYS___FSETLOCKING                   = 0xD67
	SYS___FTCHEP                        = 0x0BF
	SYS___FTELL2                        = 0xB3B
	SYS___FUPDT                         = 0x0B5
	SYS___FWRITABLE                     = 0xD64
	SYS___FWRITING                      = 0xD66
	SYS___FWRITING_UNLOCKED             = 0xD6E
	SYS___GETCB                         = 0x0B4
	SYS___GETGRGID1                     = 0xD5B
	SYS___GETGRNAM1                     = 0xD5C
	SYS___GETTHENT                      = 0xCE5
	SYS___GETTOD                        = 0xD3E
	SYS___HYPOTF_H                      = 0xAF6
	SYS___HYPOTL_H                      = 0xAF7
	SYS___ILOGBF_B                      = 0xA7F
	SYS___ILOGBF_H                      = 0xA80
	SYS___ILOGBL_B                      = 0xA82
	SYS___ILOGBL_H                      = 0xA83
	SYS___ISBLANK_A                     = 0xB2E
	SYS___ISBLNK                        = 0x0FE
	SYS___ISWBLANK_A                    = 0xB2F
	SYS___LE_CEEGTJS                    = 0xD72
	SYS___LE_TRACEBACK                  = 0xB7A
	SYS___LGAMMAL_H                     = 0xA62
	SYS___LGAMMA_B_C99                  = 0xB39
	SYS___LGAMMA_H_C99                  = 0xB38
	SYS___LGAMMA_R_C99                  = 0xB3A
	SYS___LLRINTF_B                     = 0xA88
	SYS___LLRINTF_H                     = 0xA89
	SYS___LLRINTL_B                     = 0xA8B
	SYS___LLRINTL_H                     = 0xA8C
	SYS___LLRINT_B                      = 0xA85
	SYS___LLRINT_H                      = 0xA86
	SYS___LLROUNDF_B                    = 0xA91
	SYS___LLROUNDF_H                    = 0xA92
	SYS___LLROUNDL_B                    = 0xA94
	SYS___LLROUNDL_H                    = 0xA95
	SYS___LLROUND_B                     = 0xA8E
	SYS___LLROUND_H                     = 0xA8F
	SYS___LOCALE_CTL                    = 0xD47
	SYS___LOG1PF_H                      = 0xA60
	SYS___LOG1PL_H                      = 0xA61
	SYS___LOGBF_B                       = 0xA97
	SYS___LOGBF_H                       = 0xA98
	SYS___LOGBL_B                       = 0xA9A
	SYS___LOGBL_H                       = 0xA9B
	SYS___LOGIN_APPLID                  = 0xCE2
	SYS___LRINTF_B                      = 0xAA0
	SYS___LRINTF_H                      = 0xAA1
	SYS___LRINTL_B                      = 0xAA3
	SYS___LRINTL_H                      = 0xAA4
	SYS___LRINT_B                       = 0xA9D
	SYS___LRINT_H                       = 0xA9E
	SYS___LROUNDF_FIXUP                 = 0xB31
	SYS___LROUNDL_B                     = 0xAA6
	SYS___LROUNDL_H                     = 0xAA7
	SYS___LROUND_FIXUP                  = 0xB30
	SYS___MOSERVICES                    = 0xD3D
	SYS___MUST_STAY_CLEAN               = 0xB7C
	SYS___NANF_B                        = 0xAAB
	SYS___NANL_B                        = 0xAAD
	SYS___NAN_B                         = 0xAA9
	SYS___NEARBYINTF_B                  = 0xAB2
	SYS___NEARBYINTF_H                  = 0xAB3
	SYS___NEARBYINTL_B                  = 0xAB5
	SYS___NEARBYINTL_H                  = 0xAB6
	SYS___NEARBYINT_B                   = 0xAAF
	SYS___NEARBYINT_H                   = 0xAB0
	SYS___NEXTAFTERF_B                  = 0xAB8
	SYS___NEXTAFTERF_H                  = 0xAB9
	SYS___NEXTAFTERL_B                  = 0xABB
	SYS___NEXTAFTERL_H                  = 0xABC
	SYS___NEXTTOWARDF_B                 = 0xAC1
	SYS___NEXTTOWARDF_H                 = 0xAC2
	SYS___NEXTTOWARDL_B                 = 0xAC4
	SYS___NEXTTOWARDL_H                 = 0xAC5
	SYS___NEXTTOWARD_B                  = 0xABE
	SYS___NEXTTOWARD_H                  = 0xABF
	SYS___O_ENV                         = 0xB7D
	SYS___PASSWD_APPLID                 = 0xCE3
	SYS___PTOD1                         = 0xC9E
	SYS___PTOD2                         = 0xC9F
	SYS___PTOD4                         = 0xCA0
	SYS___REGCOMP_STD                   = 0x0EA
	SYS___REMAINDERF_H                  = 0xAC6
	SYS___REMAINDERL_H                  = 0xAC7
	SYS___REMQUOD128                    = 0xC10
	SYS___REMQUOD32                     = 0xC0E
	SYS___REMQUOD64                     = 0xC0F
	SYS___REMQUOF_H                     = 0xAC9
	SYS___REMQUOL_H                     = 0xACA
	SYS___REMQUO_H                      = 0xAC8
	SYS___RINTF_B                       = 0xACC
	SYS___RINTL_B                       = 0xACE
	SYS___ROUNDF_B                      = 0xAD3
	SYS___ROUNDF_H                      = 0xAD4
	SYS___ROUNDL_B                      = 0xAD6
	SYS___ROUNDL_H                      = 0xAD7
	SYS___ROUND_B                       = 0xAD0
	SYS___ROUND_H                       = 0xAD1
	SYS___SCALBLNF_B                    = 0xADC
	SYS___SCALBLNF_H                    = 0xADD
	SYS___SCALBLNL_B                    = 0xADF
	SYS___SCALBLNL_H                    = 0xAE0
	SYS___SCALBLN_B                     = 0xAD9
	SYS___SCALBLN_H                     = 0xADA
	SYS___SCALBNF_B                     = 0xAE4
	SYS___SCALBNF_H                     = 0xAE5
	SYS___SCALBNL_B                     = 0xAE7
	SYS___SCALBNL_H                     = 0xAE8
	SYS___SCALBN_B                      = 0xAE1
	SYS___SCALBN_H                      = 0xAE2
	SYS___SETENV                        = 0x0C8
	SYS___SINPID128                     = 0xC73
	SYS___SINPID32                      = 0xC71
	SYS___SINPID64                      = 0xC72
	SYS___SMF_RECORD2                   = 0xD48
	SYS___STATIC_REINIT                 = 0xB3D
	SYS___TGAMMAF_H_C99                 = 0xB79
	SYS___TGAMMAL_H                     = 0xAE9
	SYS___TGAMMA_H_C99                  = 0xB78
	SYS___TOCSNAME2                     = 0xC9A
	SYS_CEIL                            = 0x01F
	SYS_CHAUDIT                         = 0x1E0
	SYS_EXP                             = 0x01A
	SYS_FCHAUDIT                        = 0x1E1
	SYS_FREXP                           = 0x01D
	SYS_GETGROUPSBYNAME                 = 0x1E2
	SYS_GETPWUID                        = 0x1A0
	SYS_GETUID                          = 0x1A1
	SYS_ISATTY                          = 0x1A3
	SYS_KILL                            = 0x1A4
	SYS_LDEXP                           = 0x01E
	SYS_LINK                            = 0x1A5
	SYS_LOG10                           = 0x01C
	SYS_LSEEK                           = 0x1A6
	SYS_LSTAT                           = 0x1A7
	SYS_MKDIR                           = 0x1A8
	SYS_MKFIFO                          = 0x1A9
	SYS_MKNOD                           = 0x1AA
	SYS_MODF                            = 0x01B
	SYS_MOUNT                           = 0x1AB
	SYS_OPEN                            = 0x1AC
	SYS_OPENDIR                         = 0x1AD
	SYS_PATHCONF                        = 0x1AE
	SYS_PAUSE                           = 0x1AF
	SYS_PIPE                            = 0x1B0
	SYS_PTHREAD_ATTR_DESTROY            = 0x1E7
	SYS_PTHREAD_ATTR_GETDETACHSTATE     = 0x1EB
	SYS_PTHREAD_ATTR_GETSTACKSIZE       = 0x1E9
	SYS_PTHREAD_ATTR_GETWEIGHT_NP       = 0x1ED
	SYS_PTHREAD_ATTR_INIT               = 0x1E6
	SYS_PTHREAD_ATTR_SETDETACHSTATE     = 0x1EA
	SYS_PTHREAD_ATTR_SETSTACKSIZE       = 0x1E8
	SYS_PTHREAD_ATTR_SETWEIGHT_NP       = 0x1EC
	SYS_PTHREAD_CANCEL                  = 0x1EE
	SYS_PTHREAD_CLEANUP_POP             = 0x1F0
	SYS_PTHREAD_CLEANUP_PUSH            = 0x1EF
	SYS_PTHREAD_CONDATTR_DESTROY        = 0x1F2
	SYS_PTHREAD_CONDATTR_INIT           = 0x1F1
	SYS_PTHREAD_COND_BROADCAST          = 0x1F6
	SYS_PTHREAD_COND_DESTROY            = 0x1F4
	SYS_PTHREAD_COND_INIT               = 0x1F3
	SYS_PTHREAD_COND_SIGNAL             = 0x1F5
	SYS_PTHREAD_COND_TIMEDWAIT          = 0x1F8
	SYS_PTHREAD_COND_WAIT               = 0x1F7
	SYS_PTHREAD_CREATE                  = 0x1F9
	SYS_PTHREAD_DETACH                  = 0x1FA
	SYS_PTHREAD_EQUAL                   = 0x1FB
	SYS_PTHREAD_EXIT                    = 0x1E4
	SYS_PTHREAD_GETSPECIFIC             = 0x1FC
	SYS_PTHREAD_JOIN                    = 0x1FD
	SYS_PTHREAD_KEY_CREATE              = 0x1FE
	SYS_PTHREAD_KILL                    = 0x1E5
	SYS_PTHREAD_MUTEXATTR_INIT          = 0x1FF
	SYS_READ                            = 0x1B2
	SYS_READDIR                         = 0x1B3
	SYS_READLINK                        = 0x1B4
	SYS_REWINDDIR                       = 0x1B5
	SYS_RMDIR                           = 0x1B6
	SYS_SETEGID                         = 0x1B7
	SYS_SETEUID                         = 0x1B8
	SYS_SETGID                          = 0x1B9
	SYS_SETPGID                         = 0x1BA
	SYS_SETSID                          = 0x1BB
	SYS_SETUID                          = 0x1BC
	SYS_SIGACTION                       = 0x1BD
	SYS_SIGADDSET                       = 0x1BE
	SYS_SIGDELSET                       = 0x1BF
	SYS_SIGEMPTYSET                     = 0x1C0
	SYS_SIGFILLSET                      = 0x1C1
	SYS_SIGISMEMBER                     = 0x1C2
	SYS_SIGLONGJMP                      = 0x1C3
	SYS_SIGPENDING                      = 0x1C4
	SYS_SIGPROCMASK                     = 0x1C5
	SYS_SIGSETJMP                       = 0x1C6
	SYS_SIGSUSPEND                      = 0x1C7
	SYS_SIGWAIT                         = 0x1E3
	SYS_SLEEP                           = 0x1C8
	SYS_STAT                            = 0x1C9
	SYS_SYMLINK                         = 0x1CB
	SYS_SYSCONF                         = 0x1CC
	SYS_TCDRAIN                         = 0x1CD
	SYS_TCFLOW                          = 0x1CE
	SYS_TCFLUSH                         = 0x1CF
	SYS_TCGETATTR                       = 0x1D0
	SYS_TCGETPGRP                       = 0x1D1
	SYS_TCSENDBREAK                     = 0x1D2
	SYS_TCSETATTR                       = 0x1D3
	SYS_TCSETPGRP                       = 0x1D4
	SYS_TIMES                           = 0x1D5
	SYS_TTYNAME                         = 0x1D6
	SYS_TZSET                           = 0x1D7
	SYS_UMASK                           = 0x1D8
	SYS_UMOUNT                          = 0x1D9
	SYS_UNAME                           = 0x1DA
	SYS_UNLINK                          = 0x1DB
	SYS_UTIME                           = 0x1DC
	SYS_WAIT                            = 0x1DD
	SYS_WAITPID                         = 0x1DE
	SYS_WRITE                           = 0x1DF
	SYS_W_GETPSENT                      = 0x1B1
	SYS_W_IOCTL                         = 0x1A2
	SYS_W_STATFS                        = 0x1CA
	SYS_A64L                            = 0x2EF
	SYS_BCMP                            = 0x2B9
	SYS_BCOPY                           = 0x2BA
	SYS_BZERO                           = 0x2BB
	SYS_CATCLOSE                        = 0x2B6
	SYS_CATGETS                         = 0x2B7
	SYS_CATOPEN                         = 0x2B8
	SYS_CRYPT                           = 0x2AC
	SYS_DBM_CLEARERR                    = 0x2F7
	SYS_DBM_CLOSE                       = 0x2F8
	SYS_DBM_DELETE                      = 0x2F9
	SYS_DBM_ERROR                       = 0x2FA
	SYS_DBM_FETCH                       = 0x2FB
	SYS_DBM_FIRSTKEY                    = 0x2FC
	SYS_DBM_NEXTKEY                     = 0x2FD
	SYS_DBM_OPEN                        = 0x2FE
	SYS_DBM_STORE                       = 0x2FF
	SYS_DRAND48                         = 0x2B2
	SYS_ENCRYPT                         = 0x2AD
	SYS_ENDUTXENT                       = 0x2E1
	SYS_ERAND48                         = 0x2B3
	SYS_ERF                             = 0x02C
	SYS_ERFC                            = 0x02D
	SYS_FCHDIR                          = 0x2D9
	SYS_FFS                             = 0x2BC
	SYS_FMTMSG                          = 0x2E5
	SYS_FSTATVFS                        = 0x2B4
	SYS_FTIME                           = 0x2F5
	SYS_GAMMA                           = 0x02E
	SYS_GETDATE                         = 0x2A6
	SYS_GETPAGESIZE                     = 0x2D8
	SYS_GETTIMEOFDAY                    = 0x2F6
	SYS_GETUTXENT                       = 0x2E0
	SYS_GETUTXID                        = 0x2E2
	SYS_GETUTXLINE                      = 0x2E3
	SYS_HCREATE                         = 0x2C6
	SYS_HDESTROY                        = 0x2C7
	SYS_HSEARCH                         = 0x2C8
	SYS_HYPOT                           = 0x02B
	SYS_INDEX                           = 0x2BD
	SYS_INITSTATE                       = 0x2C2
	SYS_INSQUE                          = 0x2CF
	SYS_ISASCII                         = 0x2ED
	SYS_JRAND48                         = 0x2E6
	SYS_L64A                            = 0x2F0
	SYS_LCONG48                         = 0x2EA
	SYS_LFIND                           = 0x2C9
	SYS_LRAND48                         = 0x2E7
	SYS_LSEARCH                         = 0x2CA
	SYS_MEMCCPY                         = 0x2D4
	SYS_MRAND48                         = 0x2E8
	SYS_NRAND48                         = 0x2E9
	SYS_PCLOSE                          = 0x2D2
	SYS_POPEN                           = 0x2D1
	SYS_PUTUTXLINE                      = 0x2E4
	SYS_RANDOM                          = 0x2C4
	SYS_REMQUE                          = 0x2D0
	SYS_RINDEX                          = 0x2BE
	SYS_SEED48                          = 0x2EC
	SYS_SETKEY                          = 0x2AE
	SYS_SETSTATE                        = 0x2C3
	SYS_SETUTXENT                       = 0x2DF
	SYS_SRAND48                         = 0x2EB
	SYS_SRANDOM                         = 0x2C5
	SYS_STATVFS                         = 0x2B5
	SYS_STRCASECMP                      = 0x2BF
	SYS_STRDUP                          = 0x2C0
	SYS_STRNCASECMP                     = 0x2C1
	SYS_SWAB                            = 0x2D3
	SYS_TDELETE                         = 0x2CB
	SYS_TFIND                           = 0x2CC
	SYS_TOASCII                         = 0x2EE
	SYS_TSEARCH                         = 0x2CD
	SYS_TWALK                           = 0x2CE
	SYS_UALARM                          = 0x2F1
	SYS_USLEEP                          = 0x2F2
	SYS_WAIT3                           = 0x2A7
	SYS_WAITID                          = 0x2A8
	SYS_Y1                              = 0x02A
	SYS___ATOE                          = 0x2DB
	SYS___ATOE_L                        = 0x2DC
	SYS___CATTRM                        = 0x2A9
	SYS___CNVBLK                        = 0x2AF
	SYS___CRYTRM                        = 0x2B0
	SYS___DLGHT                         = 0x2A1
	SYS___ECRTRM                        = 0x2B1
	SYS___ETOA                          = 0x2DD
	SYS___ETOA_L                        = 0x2DE
	SYS___GDTRM                         = 0x2AA
	SYS___OCLCK                         = 0x2DA
	SYS___OPARGF                        = 0x2A2
	SYS___OPERRF                        = 0x2A5
	SYS___OPINDF                        = 0x2A4
	SYS___OPOPTF                        = 0x2A3
	SYS___RNDTRM                        = 0x2AB
	SYS___SRCTRM                        = 0x2F4
	SYS___TZONE                         = 0x2A0
	SYS___UTXTRM                        = 0x2F3
	SYS_ASIN                            = 0x03E
	SYS_ISXDIGIT                        = 0x03B
	SYS_SETLOCAL                        = 0x03A
	SYS_SETLOCALE                       = 0x03A
	SYS_SIN                             = 0x03F
	SYS_TOLOWER                         = 0x03C
	SYS_TOUPPER                         = 0x03D
	SYS_ACCEPT_AND_RECV                 = 0x4F7
	SYS_ATOL                            = 0x04E
	SYS_CHECKSCH                        = 0x4BC
	SYS_CHECKSCHENV                     = 0x4BC
	SYS_CLEARERR                        = 0x04C
	SYS_CONNECTS                        = 0x4B5
	SYS_CONNECTSERVER                   = 0x4B5
	SYS_CONNECTW                        = 0x4B4
	SYS_CONNECTWORKMGR                  = 0x4B4
	SYS_CONTINUE                        = 0x4B3
	SYS_CONTINUEWORKUNIT                = 0x4B3
	SYS_COPYSIGN                        = 0x4C2
	SYS_CREATEWO                        = 0x4B2
	SYS_CREATEWORKUNIT                  = 0x4B2
	SYS_DELETEWO                        = 0x4B9
	SYS_DELETEWORKUNIT                  = 0x4B9
	SYS_DISCONNE                        = 0x4B6
	SYS_DISCONNECTSERVER                = 0x4B6
	SYS_FEOF                            = 0x04D
	SYS_FERROR                          = 0x04A
	SYS_FINITE                          = 0x4C8
	SYS_GAMMA_R                         = 0x4E2
	SYS_JOINWORK                        = 0x4B7
	SYS_JOINWORKUNIT                    = 0x4B7
	SYS_LEAVEWOR                        = 0x4B8
	SYS_LEAVEWORKUNIT                   = 0x4B8
	SYS_LGAMMA_R                        = 0x4EB
	SYS_MATHERR                         = 0x4D0
	SYS_PERROR                          = 0x04F
	SYS_QUERYMET                        = 0x4BA
	SYS_QUERYMETRICS                    = 0x4BA
	SYS_QUERYSCH                        = 0x4BB
	SYS_QUERYSCHENV                     = 0x4BB
	SYS_REWIND                          = 0x04B
	SYS_SCALBN                          = 0x4D4
	SYS_SIGNIFIC                        = 0x4D5
	SYS_SIGNIFICAND                     = 0x4D5
	SYS___ACOSH_B                       = 0x4DA
	SYS___ACOS_B                        = 0x4D9
	SYS___ASINH_B                       = 0x4BE
	SYS___ASIN_B                        = 0x4DB
	SYS___ATAN2_B                       = 0x4DC
	SYS___ATANH_B                       = 0x4DD
	SYS___ATAN_B                        = 0x4BF
	SYS___CBRT_B                        = 0x4C0
	SYS___CEIL_B                        = 0x4C1
	SYS___COSH_B                        = 0x4DE
	SYS___COS_B                         = 0x4C3
	SYS___DGHT                          = 0x4A8
	SYS___ENVN                          = 0x4B0
	SYS___ERFC_B                        = 0x4C5
	SYS___ERF_B                         = 0x4C4
	SYS___EXPM1_B                       = 0x4C6
	SYS___EXP_B                         = 0x4DF
	SYS___FABS_B                        = 0x4C7
	SYS___FLOOR_B                       = 0x4C9
	SYS___FMOD_B                        = 0x4E0
	SYS___FP_SETMODE                    = 0x4F8
	SYS___FREXP_B                       = 0x4CA
	SYS___GAMMA_B                       = 0x4E1
	SYS___GDRR                          = 0x4A1
	SYS___HRRNO                         = 0x4A2
	SYS___HYPOT_B                       = 0x4E3
	SYS___ILOGB_B                       = 0x4CB
	SYS___ISNAN_B                       = 0x4CC
	SYS___J0_B                          = 0x4E4
	SYS___J1_B                          = 0x4E6
	SYS___JN_B                          = 0x4E8
	SYS___LDEXP_B                       = 0x4CD
	SYS___LGAMMA_B                      = 0x4EA
	SYS___LOG10_B                       = 0x4ED
	SYS___LOG1P_B                       = 0x4CE
	SYS___LOGB_B                        = 0x4CF
	SYS___LOGIN                         = 0x4F5
	SYS___LOG_B                         = 0x4EC
	SYS___MLOCKALL                      = 0x4B1
	SYS___MODF_B                        = 0x4D1
	SYS___NEXTAFTER_B                   = 0x4D2
	SYS___OPENDIR2                      = 0x4F3
	SYS___OPEN_STAT                     = 0x4F6
	SYS___OPND                          = 0x4A5
	SYS___OPPT                          = 0x4A6
	SYS___OPRG                          = 0x4A3
	SYS___OPRR                          = 0x4A4
	SYS___PID_AFFINITY                  = 0x4BD
	SYS___POW_B                         = 0x4EE
	SYS___READDIR2                      = 0x4F4
	SYS___REMAINDER_B                   = 0x4EF
	SYS___RINT_B                        = 0x4D3
	SYS___SCALB_B                       = 0x4F0
	SYS___SIGACTIONSET                  = 0x4FB
	SYS___SIGGM                         = 0x4A7
	SYS___SINH_B                        = 0x4F1
	SYS___SIN_B                         = 0x4D6
	SYS___SQRT_B                        = 0x4F2
	SYS___TANH_B                        = 0x4D8
	SYS___TAN_B                         = 0x4D7
	SYS___TRRNO                         = 0x4AF
	SYS___TZNE                          = 0x4A9
	SYS___TZZN                          = 0x4AA
	SYS___UCREATE                       = 0x4FC
	SYS___UFREE                         = 0x4FE
	SYS___UHEAPREPORT                   = 0x4FF
	SYS___UMALLOC                       = 0x4FD
	SYS___Y0_B                          = 0x4E5
	SYS___Y1_B                          = 0x4E7
	SYS___YN_B                          = 0x4E9
	SYS_ABORT                           = 0x05C
	SYS_ASCTIME_R                       = 0x5E0
	SYS_ATEXIT                          = 0x05D
	SYS_CONNECTE                        = 0x5AE
	SYS_CONNECTEXPORTIMPORT             = 0x5AE
	SYS_CTIME_R                         = 0x5E1
	SYS_DN_COMP                         = 0x5DF
	SYS_DN_EXPAND                       = 0x5DD
	SYS_DN_SKIPNAME                     = 0x5DE
	SYS_EXIT                            = 0x05A
	SYS_EXPORTWO                        = 0x5A1
	SYS_EXPORTWORKUNIT                  = 0x5A1
	SYS_EXTRACTW                        = 0x5A5
	SYS_EXTRACTWORKUNIT                 = 0x5A5
	SYS_FSEEKO                          = 0x5C9
	SYS_FTELLO                          = 0x5C8
	SYS_GETGRGID_R                      = 0x5E7
	SYS_GETGRNAM_R                      = 0x5E8
	SYS_GETLOGIN_R                      = 0x5E9
	SYS_GETPWNAM_R                      = 0x5EA
	SYS_GETPWUID_R                      = 0x5EB
	SYS_GMTIME_R                        = 0x5E2
	SYS_IMPORTWO                        = 0x5A3
	SYS_IMPORTWORKUNIT                  = 0x5A3
	SYS_INET_NTOP                       = 0x5D3
	SYS_INET_PTON                       = 0x5D4
	SYS_LLABS                           = 0x5CE
	SYS_LLDIV                           = 0x5CB
	SYS_LOCALTIME_R                     = 0x5E3
	SYS_PTHREAD_ATFORK                  = 0x5ED
	SYS_PTHREAD_ATTR_GETDETACHSTATE_U98 = 0x5FB
	SYS_PTHREAD_ATTR_GETGUARDSIZE       = 0x5EE
	SYS_PTHREAD_ATTR_GETSCHEDPARAM      = 0x5F9
	SYS_PTHREAD_ATTR_GETSTACKADDR       = 0x5EF
	SYS_PTHREAD_ATTR_SETDETACHSTATE_U98 = 0x5FC
	SYS_PTHREAD_ATTR_SETGUARDSIZE       = 0x5F0
	SYS_PTHREAD_ATTR_SETSCHEDPARAM      = 0x5FA
	SYS_PTHREAD_ATTR_SETSTACKADDR       = 0x5F1
	SYS_PTHREAD_CONDATTR_GETPSHARED     = 0x5F2
	SYS_PTHREAD_CONDATTR_SETPSHARED     = 0x5F3
	SYS_PTHREAD_DETACH_U98              = 0x5FD
	SYS_PTHREAD_GETCONCURRENCY          = 0x5F4
	SYS_PTHREAD_GETSPECIFIC_U98         = 0x5FE
	SYS_PTHREAD_KEY_DELETE              = 0x5F5
	SYS_PTHREAD_SETCANCELSTATE          = 0x5FF
	SYS_PTHREAD_SETCONCURRENCY          = 0x5F6
	SYS_PTHREAD_SIGMASK                 = 0x5F7
	SYS_QUERYENC                        = 0x5AD
	SYS_QUERYWORKUNITCLASSIFICATION     = 0x5AD
	SYS_RAISE                           = 0x05E
	SYS_RAND_R                          = 0x5E4
	SYS_READDIR_R                       = 0x5E6
	SYS_REALLOC                         = 0x05B
	SYS_RES_INIT                        = 0x5D8
	SYS_RES_MKQUERY                     = 0x5D7
	SYS_RES_QUERY                       = 0x5D9
	SYS_RES_QUERYDOMAIN                 = 0x5DC
	SYS_RES_SEARCH                      = 0x5DA
	SYS_RES_SEND                        = 0x5DB
	SYS_SETJMP                          = 0x05F
	SYS_SIGQUEUE                        = 0x5A9
	SYS_STRTOK_R                        = 0x5E5
	SYS_STRTOLL                         = 0x5B0
	SYS_STRTOULL                        = 0x5B1
	SYS_TTYNAME_R                       = 0x5EC
	SYS_UNDOEXPO                        = 0x5A2
	SYS_UNDOEXPORTWORKUNIT              = 0x5A2
	SYS_UNDOIMPO                        = 0x5A4
	SYS_UNDOIMPORTWORKUNIT              = 0x5A4
	SYS_WCSTOLL                         = 0x5CC
	SYS_WCSTOULL                        = 0x5CD
	SYS___ABORT                         = 0x05C
	SYS___CONSOLE2                      = 0x5D2
	SYS___CPL                           = 0x5A6
	SYS___DISCARDDATA                   = 0x5F8
	SYS___DSA_PREV                      = 0x5B2
	SYS___EP_FIND                       = 0x5B3
	SYS___FP_SWAPMODE                   = 0x5AF
	SYS___GETUSERID                     = 0x5AB
	SYS___GET_CPUID                     = 0x5B9
	SYS___GET_SYSTEM_SETTINGS           = 0x5BA
	SYS___IPDOMAINNAME                  = 0x5AC
	SYS___MAP_INIT                      = 0x5A7
	SYS___MAP_SERVICE                   = 0x5A8
	SYS___MOUNT                         = 0x5AA
	SYS___MSGRCV_TIMED                  = 0x5B7
	SYS___RES                           = 0x5D6
	SYS___SEMOP_TIMED                   = 0x5B8
	SYS___SERVER_THREADS_QUERY          = 0x5B4
	SYS_FPRINTF                         = 0x06D
	SYS_FSCANF                          = 0x06A
	SYS_PRINTF                          = 0x06F
	SYS_SETBUF                          = 0x06B
	SYS_SETVBUF                         = 0x06C
	SYS_SSCANF                          = 0x06E
	SYS___CATGETS_A                     = 0x6C0
	SYS___CHAUDIT_A                     = 0x6F4
	SYS___CHMOD_A                       = 0x6E8
	SYS___COLLATE_INIT_A                = 0x6AC
	SYS___CREAT_A                       = 0x6F6
	SYS___CTYPE_INIT_A                  = 0x6AF
	SYS___DLLLOAD_A                     = 0x6DF
	SYS___DLLQUERYFN_A                  = 0x6E0
	SYS___DLLQUERYVAR_A                 = 0x6E1
	SYS___E2A_L                         = 0x6E3
	SYS___EXECLE_A                      = 0x6A0
	SYS___EXECLP_A                      = 0x6A4
	SYS___EXECVE_A                      = 0x6C1
	SYS___EXECVP_A                      = 0x6C2
	SYS___EXECV_A                       = 0x6B1
	SYS___FPRINTF_A                     = 0x6FA
	SYS___GETADDRINFO_A                 = 0x6BF
	SYS___GETNAMEINFO_A                 = 0x6C4
	SYS___GET_WCTYPE_STD_A              = 0x6AE
	SYS___ICONV_OPEN_A                  = 0x6DE
	SYS___IF_INDEXTONAME_A              = 0x6DC
	SYS___IF_NAMETOINDEX_A              = 0x6DB
	SYS___ISWCTYPE_A                    = 0x6B0
	SYS___IS_WCTYPE_STD_A               = 0x6B2
	SYS___LOCALECONV_A                  = 0x6B8
	SYS___LOCALECONV_STD_A              = 0x6B9
	SYS___LOCALE_INIT_A                 = 0x6B7
	SYS___LSTAT_A                       = 0x6EE
	SYS___LSTAT_O_A                     = 0x6EF
	SYS___MKDIR_A                       = 0x6E9
	SYS___MKFIFO_A                      = 0x6EC
	SYS___MKNOD_A                       = 0x6F0
	SYS___MONETARY_INIT_A               = 0x6BC
	SYS___MOUNT_A                       = 0x6F1
	SYS___NL_CSINFO_A                   = 0x6D6
	SYS___NL_LANGINFO_A                 = 0x6BA
	SYS___NL_LNAGINFO_STD_A             = 0x6BB
	SYS___NL_MONINFO_A                  = 0x6D7
	SYS___NL_NUMINFO_A                  = 0x6D8
	SYS___NL_RESPINFO_A                 = 0x6D9
	SYS___NL_TIMINFO_A                  = 0x6DA
	SYS___NUMERIC_INIT_A                = 0x6C6
	SYS___OPEN_A                        = 0x6F7
	SYS___PRINTF_A                      = 0x6DD
	SYS___RESP_INIT_A                   = 0x6C7
	SYS___RPMATCH_A                     = 0x6C8
	SYS___RPMATCH_C_A                   = 0x6C9
	SYS___RPMATCH_STD_A                 = 0x6CA
	SYS___SETLOCALE_A                   = 0x6F9
	SYS___SPAWNP_A                      = 0x6C5
	SYS___SPAWN_A                       = 0x6C3
	SYS___SPRINTF_A                     = 0x6FB
	SYS___STAT_A                        = 0x6EA
	SYS___STAT_O_A                      = 0x6EB
	SYS___STRCOLL_STD_A                 = 0x6A1
	SYS___STRFMON_A                     = 0x6BD
	SYS___STRFMON_STD_A                 = 0x6BE
	SYS___STRFTIME_A                    = 0x6CC
	SYS___STRFTIME_STD_A                = 0x6CD
	SYS___STRPTIME_A                    = 0x6CE
	SYS___STRPTIME_STD_A                = 0x6CF
	SYS___STRXFRM_A                     = 0x6A2
	SYS___STRXFRM_C_A                   = 0x6A3
	SYS___STRXFRM_STD_A                 = 0x6A5
	SYS___SYNTAX_INIT_A                 = 0x6D4
	SYS___TIME_INIT_A                   = 0x6CB
	SYS___TOD_INIT_A                    = 0x6D5
	SYS___TOWLOWER_A                    = 0x6B3
	SYS___TOWLOWER_STD_A                = 0x6B4
	SYS___TOWUPPER_A                    = 0x6B5
	SYS___TOWUPPER_STD_A                = 0x6B6
	SYS___UMOUNT_A                      = 0x6F2
	SYS___VFPRINTF_A                    = 0x6FC
	SYS___VPRINTF_A                     = 0x6FD
	SYS___VSPRINTF_A                    = 0x6FE
	SYS___VSWPRINTF_A                   = 0x6FF
	SYS___WCSCOLL_A                     = 0x6A6
	SYS___WCSCOLL_C_A                   = 0x6A7
	SYS___WCSCOLL_STD_A                 = 0x6A8
	SYS___WCSFTIME_A                    = 0x6D0
	SYS___WCSFTIME_STD_A                = 0x6D1
	SYS___WCSXFRM_A                     = 0x6A9
	SYS___WCSXFRM_C_A                   = 0x6AA
	SYS___WCSXFRM_STD_A                 = 0x6AB
	SYS___WCTYPE_A                      = 0x6AD
	SYS___W_GETMNTENT_A                 = 0x6F5
	SYS_____CCSIDTYPE_A                 = 0x6E6
	SYS_____CHATTR_A                    = 0x6E2
	SYS_____CSNAMETYPE_A                = 0x6E7
	SYS_____OPEN_STAT_A                 = 0x6ED
	SYS_____SPAWN2_A                    = 0x6D2
	SYS_____SPAWNP2_A                   = 0x6D3
	SYS_____TOCCSID_A                   = 0x6E4
	SYS_____TOCSNAME_A                  = 0x6E5
	SYS_ACL_FREE                        = 0x7FF
	SYS_ACL_INIT                        = 0x7FE
	SYS_FWIDE                           = 0x7DF
	SYS_FWPRINTF                        = 0x7D1
	SYS_FWRITE                          = 0x07E
	SYS_FWSCANF                         = 0x7D5
	SYS_GETCHAR                         = 0x07B
	SYS_GETS                            = 0x07C
	SYS_M_CREATE_LAYOUT                 = 0x7C9
	SYS_M_DESTROY_LAYOUT                = 0x7CA
	SYS_M_GETVALUES_LAYOUT              = 0x7CB
	SYS_M_SETVALUES_LAYOUT              = 0x7CC
	SYS_M_TRANSFORM_LAYOUT              = 0x7CD
	SYS_M_WTRANSFORM_LAYOUT             = 0x7CE
	SYS_PREAD                           = 0x7C7
	SYS_PUTC                            = 0x07D
	SYS_PUTCHAR                         = 0x07A
	SYS_PUTS                            = 0x07F
	SYS_PWRITE                          = 0x7C8
	SYS_TOWCTRAN                        = 0x7D8
	SYS_TOWCTRANS                       = 0x7D8
	SYS_UNATEXIT                        = 0x7B5
	SYS_VFWPRINT                        = 0x7D3
	SYS_VFWPRINTF                       = 0x7D3
	SYS_VWPRINTF                        = 0x7D4
	SYS_WCTRANS                         = 0x7D7
	SYS_WPRINTF                         = 0x7D2
	SYS_WSCANF                          = 0x7D6
	SYS___ASCTIME_R_A                   = 0x7A1
	SYS___BASENAME_A                    = 0x7DC
	SYS___BTOWC_A                       = 0x7E4
	SYS___CDUMP_A                       = 0x7B7
	SYS___CEE3DMP_A                     = 0x7B6
	SYS___CEILF_H                       = 0x7F4
	SYS___CEILL_H                       = 0x7F5
	SYS___CEIL_H                        = 0x7EA
	SYS___CRYPT_A                       = 0x7BE
	SYS___CSNAP_A                       = 0x7B8
	SYS___CTEST_A                       = 0x7B9
	SYS___CTIME_R_A                     = 0x7A2
	SYS___CTRACE_A                      = 0x7BA
	SYS___DBM_OPEN_A                    = 0x7E6
	SYS___DIRNAME_A                     = 0x7DD
	SYS___FABSF_H                       = 0x7FA
	SYS___FABSL_H                       = 0x7FB
	SYS___FABS_H                        = 0x7ED
	SYS___FGETWC_A                      = 0x7AA
	SYS___FGETWS_A                      = 0x7AD
	SYS___FLOORF_H                      = 0x7F6
	SYS___FLOORL_H                      = 0x7F7
	SYS___FLOOR_H                       = 0x7EB
	SYS___FPUTWC_A                      = 0x7A5
	SYS___FPUTWS_A                      = 0x7A8
	SYS___GETTIMEOFDAY_A                = 0x7AE
	SYS___GETWCHAR_A                    = 0x7AC
	SYS___GETWC_A                       = 0x7AB
	SYS___GLOB_A                        = 0x7DE
	SYS___GMTIME_A                      = 0x7AF
	SYS___GMTIME_R_A                    = 0x7B0
	SYS___INET_PTON_A                   = 0x7BC
	SYS___J0_H                          = 0x7EE
	SYS___J1_H                          = 0x7EF
	SYS___JN_H                          = 0x7F0
	SYS___LOCALTIME_A                   = 0x7B1
	SYS___LOCALTIME_R_A                 = 0x7B2
	SYS___MALLOC24                      = 0x7FC
	SYS___MALLOC31                      = 0x7FD
	SYS___MKTIME_A                      = 0x7B3
	SYS___MODFF_H                       = 0x7F8
	SYS___MODFL_H                       = 0x7F9
	SYS___MODF_H                        = 0x7EC
	SYS___OPENDIR_A                     = 0x7C2
	SYS___OSNAME                        = 0x7E0
	SYS___PUTWCHAR_A                    = 0x7A7
	SYS___PUTWC_A                       = 0x7A6
	SYS___READDIR_A                     = 0x7C3
	SYS___STRTOLL_A                     = 0x7A3
	SYS___STRTOULL_A                    = 0x7A4
	SYS___SYSLOG_A                      = 0x7BD
	SYS___TZZNA                         = 0x7B4
	SYS___UNGETWC_A                     = 0x7A9
	SYS___UTIME_A                       = 0x7A0
	SYS___VFPRINTF2_A                   = 0x7E7
	SYS___VPRINTF2_A                    = 0x7E8
	SYS___VSPRINTF2_A                   = 0x7E9
	SYS___VSWPRNTF2_A                   = 0x7BB
	SYS___WCSTOD_A                      = 0x7D9
	SYS___WCSTOL_A                      = 0x7DA
	SYS___WCSTOUL_A                     = 0x7DB
	SYS___WCTOB_A                       = 0x7E5
	SYS___Y0_H                          = 0x7F1
	SYS___Y1_H                          = 0x7F2
	SYS___YN_H                          = 0x7F3
	SYS_____OPENDIR2_A                  = 0x7BF
	SYS_____OSNAME_A                    = 0x7E1
	SYS_____READDIR2_A                  = 0x7C0
	SYS_DLCLOSE                         = 0x8DF
	SYS_DLERROR                         = 0x8E0
	SYS_DLOPEN                          = 0x8DD
	SYS_DLSYM                           = 0x8DE
	SYS_FLOCKFILE                       = 0x8D3
	SYS_FTRYLOCKFILE                    = 0x8D4
	SYS_FUNLOCKFILE                     = 0x8D5
	SYS_GETCHAR_UNLOCKED                = 0x8D7
	SYS_GETC_UNLOCKED                   = 0x8D6
	SYS_PUTCHAR_UNLOCKED                = 0x8D9
	SYS_PUTC_UNLOCKED                   = 0x8D8
	SYS_SNPRINTF                        = 0x8DA
	SYS_VSNPRINTF                       = 0x8DB
	SYS_WCSCSPN                         = 0x08B
	SYS_WCSLEN                          = 0x08C
	SYS_WCSNCAT                         = 0x08D
	SYS_WCSNCMP                         = 0x08A
	SYS_WCSNCPY                         = 0x08F
	SYS_WCSSPN                          = 0x08E
	SYS___ABSF_H                        = 0x8E7
	SYS___ABSL_H                        = 0x8E8
	SYS___ABS_H                         = 0x8E6
	SYS___ACOSF_H                       = 0x8EA
	SYS___ACOSH_H                       = 0x8EC
	SYS___ACOSL_H                       = 0x8EB
	SYS___ACOS_H                        = 0x8E9
	SYS___ASINF_H                       = 0x8EE
	SYS___ASINH_H                       = 0x8F0
	SYS___ASINL_H                       = 0x8EF
	SYS___ASIN_H                        = 0x8ED
	SYS___ATAN2F_H                      = 0x8F8
	SYS___ATAN2L_H                      = 0x8F9
	SYS___ATAN2_H                       = 0x8F7
	SYS___ATANF_H                       = 0x8F2
	SYS___ATANHF_H                      = 0x8F5
	SYS___ATANHL_H                      = 0x8F6
	SYS___ATANH_H                       = 0x8F4
	SYS___ATANL_H                       = 0x8F3
	SYS___ATAN_H                        = 0x8F1
	SYS___CBRT_H                        = 0x8FA
	SYS___COPYSIGNF_H                   = 0x8FB
	SYS___COPYSIGNL_H                   = 0x8FC
	SYS___COSF_H                        = 0x8FE
	SYS___COSL_H                        = 0x8FF
	SYS___COS_H                         = 0x8FD
	SYS___DLERROR_A                     = 0x8D2
	SYS___DLOPEN_A                      = 0x8D0
	SYS___DLSYM_A                       = 0x8D1
	SYS___GETUTXENT_A                   = 0x8C6
	SYS___GETUTXID_A                    = 0x8C7
	SYS___GETUTXLINE_A                  = 0x8C8
	SYS___ITOA                          = 0x8AA
	SYS___ITOA_A                        = 0x8B0
	SYS___LE_CONDITION_TOKEN_BUILD      = 0x8A5
	SYS___LE_MSG_ADD_INSERT             = 0x8A6
	SYS___LE_MSG_GET                    = 0x8A7
	SYS___LE_MSG_GET_AND_WRITE          = 0x8A8
	SYS___LE_MSG_WRITE                  = 0x8A9
	SYS___LLTOA                         = 0x8AE
	SYS___LLTOA_A                       = 0x8B4
	SYS___LTOA                          = 0x8AC
	SYS___LTOA_A                        = 0x8B2
	SYS___PUTCHAR_UNLOCKED_A            = 0x8CC
	SYS___PUTC_UNLOCKED_A               = 0x8CB
	SYS___PUTUTXLINE_A                  = 0x8C9
	SYS___RESET_EXCEPTION_HANDLER       = 0x8E3
	SYS___REXEC_A                       = 0x8C4
	SYS___REXEC_AF_A                    = 0x8C5
	SYS___SET_EXCEPTION_HANDLER         = 0x8E2
	SYS___SNPRINTF_A                    = 0x8CD
	SYS___SUPERKILL                     = 0x8A4
	SYS___TCGETATTR_A                   = 0x8A1
	SYS___TCSETATTR_A                   = 0x8A2
	SYS___ULLTOA                        = 0x8AF
	SYS___ULLTOA_A                      = 0x8B5
	SYS___ULTOA                         = 0x8AD
	SYS___ULTOA_A                       = 0x8B3
	SYS___UTOA                          = 0x8AB
	SYS___UTOA_A                        = 0x8B1
	SYS___VHM_EVENT                     = 0x8E4
	SYS___VSNPRINTF_A                   = 0x8CE
	SYS_____GETENV_A                    = 0x8C3
	SYS_____UTMPXNAME_A                 = 0x8CA
	SYS_CACOSH                          = 0x9A0
	SYS_CACOSHF                         = 0x9A3
	SYS_CACOSHL                         = 0x9A6
	SYS_CARG                            = 0x9A9
	SYS_CARGF                           = 0x9AC
	SYS_CARGL                           = 0x9AF
	SYS_CASIN                           = 0x9B2
	SYS_CASINF                          = 0x9B5
	SYS_CASINH                          = 0x9BB
	SYS_CASINHF                         = 0x9BE
	SYS_CASINHL                         = 0x9C1
	SYS_CASINL                          = 0x9B8
	SYS_CATAN                           = 0x9C4
	SYS_CATANF                          = 0x9C7
	SYS_CATANH                          = 0x9CD
	SYS_CATANHF                         = 0x9D0
	SYS_CATANHL                         = 0x9D3
	SYS_CATANL                          = 0x9CA
	SYS_CCOS                            = 0x9D6
	SYS_CCOSF                           = 0x9D9
	SYS_CCOSH                           = 0x9DF
	SYS_CCOSHF                          = 0x9E2
	SYS_CCOSHL                          = 0x9E5
	SYS_CCOSL                           = 0x9DC
	SYS_CEXP                            = 0x9E8
	SYS_CEXPF                           = 0x9EB
	SYS_CEXPL                           = 0x9EE
	SYS_CIMAG                           = 0x9F1
	SYS_CIMAGF                          = 0x9F4
	SYS_CIMAGL                          = 0x9F7
	SYS_CLOGF                           = 0x9FD
	SYS_MEMCHR                          = 0x09B
	SYS_MEMCMP                          = 0x09A
	SYS_STRCOLL                         = 0x09C
	SYS_STRNCMP                         = 0x09D
	SYS_STRRCHR                         = 0x09F
	SYS_STRXFRM                         = 0x09E
	SYS___CACOSHF_B                     = 0x9A4
	SYS___CACOSHF_H                     = 0x9A5
	SYS___CACOSHL_B                     = 0x9A7
	SYS___CACOSHL_H                     = 0x9A8
	SYS___CACOSH_B                      = 0x9A1
	SYS___CACOSH_H                      = 0x9A2
	SYS___CARGF_B                       = 0x9AD
	SYS___CARGF_H                       = 0x9AE
	SYS___CARGL_B                       = 0x9B0
	SYS___CARGL_H                       = 0x9B1
	SYS___CARG_B                        = 0x9AA
	SYS___CARG_H                        = 0x9AB
	SYS___CASINF_B                      = 0x9B6
	SYS___CASINF_H                      = 0x9B7
	SYS___CASINHF_B                     = 0x9BF
	SYS___CASINHF_H                     = 0x9C0
	SYS___CASINHL_B                     = 0x9C2
	SYS___CASINHL_H                     = 0x9C3
	SYS___CASINH_B                      = 0x9BC
	SYS___CASINH_H                      = 0x9BD
	SYS___CASINL_B                      = 0x9B9
	SYS___CASINL_H                      = 0x9BA
	SYS___CASIN_B                       = 0x9B3
	SYS___CASIN_H                       = 0x9B4
	SYS___CATANF_B                      = 0x9C8
	SYS___CATANF_H                      = 0x9C9
	SYS___CATANHF_B                     = 0x9D1
	SYS___CATANHF_H                     = 0x9D2
	SYS___CATANHL_B                     = 0x9D4
	SYS___CATANHL_H                     = 0x9D5
	SYS___CATANH_B                      = 0x9CE
	SYS___CATANH_H                      = 0x9CF
	SYS___CATANL_B                      = 0x9CB
	SYS___CATANL_H                      = 0x9CC
	SYS___CATAN_B                       = 0x9C5
	SYS___CATAN_H                       = 0x9C6
	SYS___CCOSF_B                       = 0x9DA
	SYS___CCOSF_H                       = 0x9DB
	SYS___CCOSHF_B                      = 0x9E3
	SYS___CCOSHF_H                      = 0x9E4
	SYS___CCOSHL_B                      = 0x9E6
	SYS___CCOSHL_H                      = 0x9E7
	SYS___CCOSH_B                       = 0x9E0
	SYS___CCOSH_H                       = 0x9E1
	SYS___CCOSL_B                       = 0x9DD
	SYS___CCOSL_H                       = 0x9DE
	SYS___CCOS_B                        = 0x9D7
	SYS___CCOS_H                        = 0x9D8
	SYS___CEXPF_B                       = 0x9EC
	SYS___CEXPF_H                       = 0x9ED
	SYS___CEXPL_B                       = 0x9EF
	SYS___CEXPL_H                       = 0x9F0
	SYS___CEXP_B                        = 0x9E9
	SYS___CEXP_H                        = 0x9EA
	SYS___CIMAGF_B                      = 0x9F5
	SYS___CIMAGF_H                      = 0x9F6
	SYS___CIMAGL_B                      = 0x9F8
	SYS___CIMAGL_H                      = 0x9F9
	SYS___CIMAG_B                       = 0x9F2
	SYS___CIMAG_H                       = 0x9F3
	SYS___CLOG                          = 0x9FA
	SYS___CLOGF_B                       = 0x9FE
	SYS___CLOGF_H                       = 0x9FF
	SYS___CLOG_B                        = 0x9FB
	SYS___CLOG_H                        = 0x9FC
	SYS_ISWCTYPE                        = 0x10C
	SYS_ISWXDIGI                        = 0x10A
	SYS_ISWXDIGIT                       = 0x10A
	SYS_MBSINIT                         = 0x10F
	SYS_TOWLOWER                        = 0x10D
	SYS_TOWUPPER                        = 0x10E
	SYS_WCTYPE                          = 0x10B
	SYS_WCSSTR                          = 0x11B
	SYS___RPMTCH                        = 0x11A
	SYS_WCSTOD                          = 0x12E
	SYS_WCSTOK                          = 0x12C
	SYS_WCSTOL                          = 0x12D
	SYS_WCSTOUL                         = 0x12F
	SYS_FGETWC                          = 0x13C
	SYS_FGETWS                          = 0x13D
	SYS_FPUTWC                          = 0x13E
	SYS_FPUTWS                          = 0x13F
	SYS_REGERROR                        = 0x13B
	SYS_REGFREE                         = 0x13A
	SYS_COLLEQUIV                       = 0x14F
	SYS_COLLTOSTR                       = 0x14E
	SYS_ISMCCOLLEL                      = 0x14C
	SYS_STRTOCOLL                       = 0x14D
	SYS_DLLFREE                         = 0x16F
	SYS_DLLQUERYFN                      = 0x16D
	SYS_DLLQUERYVAR                     = 0x16E
	SYS_GETMCCOLL                       = 0x16A
	SYS_GETWMCCOLL                      = 0x16B
	SYS___ERR2AD                        = 0x16C
	SYS_CFSETOSPEED                     = 0x17A
	SYS_CHDIR                           = 0x17B
	SYS_CHMOD                           = 0x17C
	SYS_CHOWN                           = 0x17D
	SYS_CLOSE                           = 0x17E
	SYS_CLOSEDIR                        = 0x17F
	SYS_LOG                             = 0x017
	SYS_COSH                            = 0x018
	SYS_FCHMOD                          = 0x18A
	SYS_FCHOWN                          = 0x18B
	SYS_FCNTL                           = 0x18C
	SYS_FILENO                          = 0x18D
	SYS_FORK                            = 0x18E
	SYS_FPATHCONF                       = 0x18F
	SYS_GETLOGIN                        = 0x19A
	SYS_GETPGRP                         = 0x19C
	SYS_GETPID                          = 0x19D
	SYS_GETPPID                         = 0x19E
	SYS_GETPWNAM                        = 0x19F
	SYS_TANH                            = 0x019
	SYS_W_GETMNTENT                     = 0x19B
	SYS_POW                             = 0x020
	SYS_PTHREAD_SELF                    = 0x20A
	SYS_PTHREAD_SETINTR                 = 0x20B
	SYS_PTHREAD_SETINTRTYPE             = 0x20C
	SYS_PTHREAD_SETSPECIFIC             = 0x20D
	SYS_PTHREAD_TESTINTR                = 0x20E
	SYS_PTHREAD_YIELD                   = 0x20F
	SYS_SQRT                            = 0x021
	SYS_FLOOR                           = 0x022
	SYS_J1                              = 0x023
	SYS_WCSPBRK                         = 0x23F
	SYS_BSEARCH                         = 0x24C
	SYS_FABS                            = 0x024
	SYS_GETENV                          = 0x24A
	SYS_LDIV                            = 0x24D
	SYS_SYSTEM                          = 0x24B
	SYS_FMOD                            = 0x025
	SYS___RETHROW                       = 0x25F
	SYS___THROW                         = 0x25E
	SYS_J0                              = 0x026
	SYS_PUTENV                          = 0x26A
	SYS___GETENV                        = 0x26F
	SYS_SEMCTL                          = 0x27A
	SYS_SEMGET                          = 0x27B
	SYS_SEMOP                           = 0x27C
	SYS_SHMAT                           = 0x27D
	SYS_SHMCTL                          = 0x27E
	SYS_SHMDT                           = 0x27F
	SYS_YN                              = 0x027
	SYS_JN                              = 0x028
	SYS_SIGALTSTACK                     = 0x28A
	SYS_SIGHOLD                         = 0x28B
	SYS_SIGIGNORE                       = 0x28C
	SYS_SIGINTERRUPT                    = 0x28D
	SYS_SIGPAUSE                        = 0x28E
	SYS_SIGRELSE                        = 0x28F
	SYS_GETOPT                          = 0x29A
	SYS_GETSUBOPT                       = 0x29D
	SYS_LCHOWN                          = 0x29B
	SYS_SETPGRP                         = 0x29E
	SYS_TRUNCATE                        = 0x29C
	SYS_Y0                              = 0x029
	SYS___GDERR                         = 0x29F
	SYS_ISALPHA                         = 0x030
	SYS_VFORK                           = 0x30F
	SYS__LONGJMP                        = 0x30D
	SYS__SETJMP                         = 0x30E
	SYS_GLOB                            = 0x31A
	SYS_GLOBFREE                        = 0x31B
	SYS_ISALNUM                         = 0x031
	SYS_PUTW                            = 0x31C
	SYS_SEEKDIR                         = 0x31D
	SYS_TELLDIR                         = 0x31E
	SYS_TEMPNAM                         = 0x31F
	SYS_GETTIMEOFDAY_R                  = 0x32E
	SYS_ISLOWER                         = 0x032
	SYS_LGAMMA                          = 0x32C
	SYS_REMAINDER                       = 0x32A
	SYS_SCALB                           = 0x32B
	SYS_SYNC                            = 0x32F
	SYS_TTYSLOT                         = 0x32D
	SYS_ENDPROTOENT                     = 0x33A
	SYS_ENDSERVENT                      = 0x33B
	SYS_GETHOSTBYADDR                   = 0x33D
	SYS_GETHOSTBYADDR_R                 = 0x33C
	SYS_GETHOSTBYNAME                   = 0x33F
	SYS_GETHOSTBYNAME_R                 = 0x33E
	SYS_ISCNTRL                         = 0x033
	SYS_GETSERVBYNAME                   = 0x34A
	SYS_GETSERVBYPORT                   = 0x34B
	SYS_GETSERVENT                      = 0x34C
	SYS_GETSOCKNAME                     = 0x34D
	SYS_GETSOCKOPT                      = 0x34E
	SYS_INET_ADDR                       = 0x34F
	SYS_ISDIGIT                         = 0x034
	SYS_ISGRAPH                         = 0x035
	SYS_SELECT                          = 0x35B
	SYS_SELECTEX                        = 0x35C
	SYS_SEND                            = 0x35D
	SYS_SENDTO                          = 0x35F
	SYS_CHROOT                          = 0x36A
	SYS_ISNAN                           = 0x36D
	SYS_ISUPPER                         = 0x036
	SYS_ULIMIT                          = 0x36C
	SYS_UTIMES                          = 0x36E
	SYS_W_STATVFS                       = 0x36B
	SYS___H_ERRNO                       = 0x36F
	SYS_GRANTPT                         = 0x37A
	SYS_ISPRINT                         = 0x037
	SYS_TCGETSID                        = 0x37C
	SYS_UNLOCKPT                        = 0x37B
	SYS___TCGETCP                       = 0x37D
	SYS___TCSETCP                       = 0x37E
	SYS___TCSETTABLES                   = 0x37F
	SYS_ISPUNCT                         = 0x038
	SYS_NLIST                           = 0x38C
	SYS___IPDBCS                        = 0x38D
	SYS___IPDSPX                        = 0x38E
	SYS___IPMSGC                        = 0x38F
	SYS___STHOSTENT                     = 0x38B
	SYS___STSERVENT                     = 0x38A
	SYS_ISSPACE                         = 0x039
	SYS_COS                             = 0x040
	SYS_T_ALLOC                         = 0x40A
	SYS_T_BIND                          = 0x40B
	SYS_T_CLOSE                         = 0x40C
	SYS_T_CONNECT                       = 0x40D
	SYS_T_ERROR                         = 0x40E
	SYS_T_FREE                          = 0x40F
	SYS_TAN                             = 0x041
	SYS_T_RCVREL                        = 0x41A
	SYS_T_RCVUDATA                      = 0x41B
	SYS_T_RCVUDERR                      = 0x41C
	SYS_T_SND                           = 0x41D
	SYS_T_SNDDIS                        = 0x41E
	SYS_T_SNDREL                        = 0x41F
	SYS_GETPMSG                         = 0x42A
	SYS_ISASTREAM                       = 0x42B
	SYS_PUTMSG                          = 0x42C
	SYS_PUTPMSG                         = 0x42D
	SYS_SINH                            = 0x042
	SYS___ISPOSIXON                     = 0x42E
	SYS___OPENMVSREL                    = 0x42F
	SYS_ACOS                            = 0x043
	SYS_ATAN                            = 0x044
	SYS_ATAN2                           = 0x045
	SYS_FTELL                           = 0x046
	SYS_FGETPOS                         = 0x047
	SYS_SOCK_DEBUG                      = 0x47A
	SYS_SOCK_DO_TESTSTOR                = 0x47D
	SYS_TAKESOCKET                      = 0x47E
	SYS___SERVER_INIT                   = 0x47F
	SYS_FSEEK                           = 0x048
	SYS___IPHOST                        = 0x48B
	SYS___IPNODE                        = 0x48C
	SYS___SERVER_CLASSIFY_CREATE        = 0x48D
	SYS___SERVER_CLASSIFY_DESTROY       = 0x48E
	SYS___SERVER_CLASSIFY_RESET         = 0x48F
	SYS___SMF_RECORD                    = 0x48A
	SYS_FSETPOS                         = 0x049
	SYS___FNWSA                         = 0x49B
	SYS___SPAWN2                        = 0x49D
	SYS___SPAWNP2                       = 0x49E
	SYS_ATOF                            = 0x050
	SYS_PTHREAD_MUTEXATTR_GETPSHARED    = 0x50A
	SYS_PTHREAD_MUTEXATTR_SETPSHARED    = 0x50B
	SYS_PTHREAD_RWLOCK_DESTROY          = 0x50C
	SYS_PTHREAD_RWLOCK_INIT             = 0x50D
	SYS_PTHREAD_RWLOCK_RDLOCK           = 0x50E
	SYS_PTHREAD_RWLOCK_TRYRDLOCK        = 0x50F
	SYS_ATOI                            = 0x051
	SYS___FP_CLASS                      = 0x51D
	SYS___FP_CLR_FLAG                   = 0x51A
	SYS___FP_FINITE                     = 0x51E
	SYS___FP_ISNAN                      = 0x51F
	SYS___FP_RAISE_XCP                  = 0x51C
	SYS___FP_READ_FLAG                  = 0x51B
	SYS_RAND                            = 0x052
	SYS_SIGTIMEDWAIT                    = 0x52D
	SYS_SIGWAITINFO                     = 0x52E
	SYS___CHKBFP                        = 0x52F
	SYS___FPC_RS                        = 0x52C
	SYS___FPC_RW                        = 0x52A
	SYS___FPC_SM                        = 0x52B
	SYS_STRTOD                          = 0x053
	SYS_STRTOL                          = 0x054
	SYS_STRTOUL                         = 0x055
	SYS_MALLOC                          = 0x056
	SYS_SRAND                           = 0x057
	SYS_CALLOC                          = 0x058
	SYS_FREE                            = 0x059
	SYS___OSENV                         = 0x59F
	SYS___W_PIOCTL                      = 0x59E
	SYS_LONGJMP                         = 0x060
	SYS___FLOORF_B                      = 0x60A
	SYS___FLOORL_B                      = 0x60B
	SYS___FREXPF_B                      = 0x60C
	SYS___FREXPL_B                      = 0x60D
	SYS___LDEXPF_B                      = 0x60E
	SYS___LDEXPL_B                      = 0x60F
	SYS_SIGNAL                          = 0x061
	SYS___ATAN2F_B                      = 0x61A
	SYS___ATAN2L_B                      = 0x61B
	SYS___COSHF_B                       = 0x61C
	SYS___COSHL_B                       = 0x61D
	SYS___EXPF_B                        = 0x61E
	SYS___EXPL_B                        = 0x61F
	SYS_TMPNAM                          = 0x062
	SYS___ABSF_B                        = 0x62A
	SYS___ABSL_B                        = 0x62C
	SYS___ABS_B                         = 0x62B
	SYS___FMODF_B                       = 0x62D
	SYS___FMODL_B                       = 0x62E
	SYS___MODFF_B                       = 0x62F
	SYS_ATANL                           = 0x63A
	SYS_CEILF                           = 0x63B
	SYS_CEILL                           = 0x63C
	SYS_COSF                            = 0x63D
	SYS_COSHF                           = 0x63F
	SYS_COSL                            = 0x63E
	SYS_REMOVE                          = 0x063
	SYS_POWL                            = 0x64A
	SYS_RENAME                          = 0x064
	SYS_SINF                            = 0x64B
	SYS_SINHF                           = 0x64F
	SYS_SINL                            = 0x64C
	SYS_SQRTF                           = 0x64D
	SYS_SQRTL                           = 0x64E
	SYS_BTOWC                           = 0x65F
	SYS_FREXPL                          = 0x65A
	SYS_LDEXPF                          = 0x65B
	SYS_LDEXPL                          = 0x65C
	SYS_MODFF                           = 0x65D
	SYS_MODFL                           = 0x65E
	SYS_TMPFILE                         = 0x065
	SYS_FREOPEN                         = 0x066
	SYS___CHARMAP_INIT_A                = 0x66E
	SYS___GETHOSTBYADDR_R_A             = 0x66C
	SYS___GETHOSTBYNAME_A               = 0x66A
	SYS___GETHOSTBYNAME_R_A             = 0x66D
	SYS___MBLEN_A                       = 0x66F
	SYS___RES_INIT_A                    = 0x66B
	SYS_FCLOSE                          = 0x067
	SYS___GETGRGID_R_A                  = 0x67D
	SYS___WCSTOMBS_A                    = 0x67A
	SYS___WCSTOMBS_STD_A                = 0x67B
	SYS___WCSWIDTH_A                    = 0x67C
	SYS___WCSWIDTH_ASIA                 = 0x67F
	SYS___WCSWIDTH_STD_A                = 0x67E
	SYS_FFLUSH                          = 0x068
	SYS___GETLOGIN_R_A                  = 0x68E
	SYS___GETPWNAM_R_A                  = 0x68C
	SYS___GETPWUID_R_A                  = 0x68D
	SYS___TTYNAME_R_A                   = 0x68F
	SYS___WCWIDTH_ASIA                  = 0x68B
	SYS___WCWIDTH_STD_A                 = 0x68A
	SYS_FOPEN                           = 0x069
	SYS___REGEXEC_A                     = 0x69A
	SYS___REGEXEC_STD_A                 = 0x69B
	SYS___REGFREE_A                     = 0x69C
	SYS___REGFREE_STD_A                 = 0x69D
	SYS___STRCOLL_A                     = 0x69E
	SYS___STRCOLL_C_A                   = 0x69F
	SYS_SCANF                           = 0x070
	SYS___A64L_A                        = 0x70C
	SYS___ECVT_A                        = 0x70D
	SYS___FCVT_A                        = 0x70E
	SYS___GCVT_A                        = 0x70F
	SYS___STRTOUL_A                     = 0x70A
	SYS_____AE_CORRESTBL_QUERY_A        = 0x70B
	SYS_SPRINTF                         = 0x071
	SYS___ACCESS_A                      = 0x71F
	SYS___CATOPEN_A                     = 0x71E
	SYS___GETOPT_A                      = 0x71D
	SYS___REALPATH_A                    = 0x71A
	SYS___SETENV_A                      = 0x71B
	SYS___SYSTEM_A                      = 0x71C
	SYS_FGETC                           = 0x072
	SYS___GAI_STRERROR_A                = 0x72F
	SYS___RMDIR_A                       = 0x72A
	SYS___STATVFS_A                     = 0x72B
	SYS___SYMLINK_A                     = 0x72C
	SYS___TRUNCATE_A                    = 0x72D
	SYS___UNLINK_A                      = 0x72E
	SYS_VFPRINTF                        = 0x073
	SYS___ISSPACE_A                     = 0x73A
	SYS___ISUPPER_A                     = 0x73B
	SYS___ISWALNUM_A                    = 0x73F
	SYS___ISXDIGIT_A                    = 0x73C
	SYS___TOLOWER_A                     = 0x73D
	SYS___TOUPPER_A                     = 0x73E
	SYS_VPRINTF                         = 0x074
	SYS___CONFSTR_A                     = 0x74B
	SYS___FDOPEN_A                      = 0x74E
	SYS___FLDATA_A                      = 0x74F
	SYS___FTOK_A                        = 0x74C
	SYS___ISWXDIGIT_A                   = 0x74A
	SYS___MKTEMP_A                      = 0x74D
	SYS_VSPRINTF                        = 0x075
	SYS___GETGRGID_A                    = 0x75A
	SYS___GETGRNAM_A                    = 0x75B
	SYS___GETGROUPSBYNAME_A             = 0x75C
	SYS___GETHOSTENT_A                  = 0x75D
	SYS___GETHOSTNAME_A                 = 0x75E
	SYS___GETLOGIN_A                    = 0x75F
	SYS_GETC                            = 0x076
	SYS___CREATEWORKUNIT_A              = 0x76A
	SYS___CTERMID_A                     = 0x76B
	SYS___FMTMSG_A                      = 0x76C
	SYS___INITGROUPS_A                  = 0x76D
	SYS___MSGRCV_A                      = 0x76F
	SYS_____LOGIN_A                     = 0x76E
	SYS_FGETS                           = 0x077
	SYS___STRCASECMP_A                  = 0x77B
	SYS___STRNCASECMP_A                 = 0x77C
	SYS___TTYNAME_A                     = 0x77D
	SYS___UNAME_A                       = 0x77E
	SYS___UTIMES_A                      = 0x77F
	SYS_____SERVER_PWU_A                = 0x77A
	SYS_FPUTC                           = 0x078
	SYS___CREAT_O_A                     = 0x78E
	SYS___ENVNA                         = 0x78F
	SYS___FREAD_A                       = 0x78A
	SYS___FWRITE_A                      = 0x78B
	SYS___ISASCII                       = 0x78D
	SYS___OPEN_O_A                      = 0x78C
	SYS_FPUTS                           = 0x079
	SYS___ASCTIME_A                     = 0x79C
	SYS___CTIME_A                       = 0x79D
	SYS___GETDATE_A                     = 0x79E
	SYS___GETSERVBYPORT_A               = 0x79A
	SYS___GETSERVENT_A                  = 0x79B
	SYS___TZSET_A                       = 0x79F
	SYS_ACL_FROM_TEXT                   = 0x80C
	SYS_ACL_SET_FD                      = 0x80A
	SYS_ACL_SET_FILE                    = 0x80B
	SYS_ACL_SORT                        = 0x80E
	SYS_ACL_TO_TEXT                     = 0x80D
	SYS_UNGETC                          = 0x080
	SYS___SHUTDOWN_REGISTRATION         = 0x80F
	SYS_FREAD                           = 0x081
	SYS_FREEADDRINFO                    = 0x81A
	SYS_GAI_STRERROR                    = 0x81B
	SYS_REXEC_AF                        = 0x81C
	SYS___DYNALLOC_A                    = 0x81F
	SYS___POE                           = 0x81D
	SYS_WCSTOMBS                        = 0x082
	SYS___INET_ADDR_A                   = 0x82F
	SYS___NLIST_A                       = 0x82A
	SYS_____TCGETCP_A                   = 0x82B
	SYS_____TCSETCP_A                   = 0x82C
	SYS_____W_PIOCTL_A                  = 0x82E
	SYS_MBTOWC                          = 0x083
	SYS___CABEND                        = 0x83D
	SYS___LE_CIB_GET                    = 0x83E
	SYS___RECVMSG_A                     = 0x83B
	SYS___SENDMSG_A                     = 0x83A
	SYS___SET_LAA_FOR_JIT               = 0x83F
	SYS_____LCHATTR_A                   = 0x83C
	SYS_WCTOMB                          = 0x084
	SYS___CBRTL_B                       = 0x84A
	SYS___COPYSIGNF_B                   = 0x84B
	SYS___COPYSIGNL_B                   = 0x84C
	SYS___COTANF_B                      = 0x84D
	SYS___COTANL_B                      = 0x84F
	SYS___COTAN_B                       = 0x84E
	SYS_MBSTOWCS                        = 0x085
	SYS___LOG1PL_B                      = 0x85A
	SYS___LOG2F_B                       = 0x85B
	SYS___LOG2L_B                       = 0x85D
	SYS___LOG2_B                        = 0x85C
	SYS___REMAINDERF_B                  = 0x85E
	SYS___REMAINDERL_B                  = 0x85F
	SYS_ACOSHF                          = 0x86E
	SYS_ACOSHL                          = 0x86F
	SYS_WCSCPY                          = 0x086
	SYS___ERFCF_B                       = 0x86D
	SYS___ERFF_B                        = 0x86C
	SYS___LROUNDF_B                     = 0x86A
	SYS___LROUND_B                      = 0x86B
	SYS_COTANL                          = 0x87A
	SYS_EXP2F                           = 0x87B
	SYS_EXP2L                           = 0x87C
	SYS_EXPM1F                          = 0x87D
	SYS_EXPM1L                          = 0x87E
	SYS_FDIMF                           = 0x87F
	SYS_WCSCAT                          = 0x087
	SYS___COTANL                        = 0x87A
	SYS_REMAINDERF                      = 0x88A
	SYS_REMAINDERL                      = 0x88B
	SYS_REMAINDF                        = 0x88A
	SYS_REMAINDL                        = 0x88B
	SYS_REMQUO                          = 0x88D
	SYS_REMQUOF                         = 0x88C
	SYS_REMQUOL                         = 0x88E
	SYS_TGAMMAF                         = 0x88F
	SYS_WCSCHR                          = 0x088
	SYS_ERFCF                           = 0x89B
	SYS_ERFCL                           = 0x89C
	SYS_ERFL                            = 0x89A
	SYS_EXP2                            = 0x89E
	SYS_WCSCMP                          = 0x089
	SYS___EXP2_B                        = 0x89D
	SYS___FAR_JUMP                      = 0x89F
	SYS_ABS                             = 0x090
	SYS___ERFCL_H                       = 0x90A
	SYS___EXPF_H                        = 0x90C
	SYS___EXPL_H                        = 0x90D
	SYS___EXPM1_H                       = 0x90E
	SYS___EXP_H                         = 0x90B
	SYS___FDIM_H                        = 0x90F
	SYS_DIV                             = 0x091
	SYS___LOG2F_H                       = 0x91F
	SYS___LOG2_H                        = 0x91E
	SYS___LOGB_H                        = 0x91D
	SYS___LOGF_H                        = 0x91B
	SYS___LOGL_H                        = 0x91C
	SYS___LOG_H                         = 0x91A
	SYS_LABS                            = 0x092
	SYS___POWL_H                        = 0x92A
	SYS___REMAINDER_H                   = 0x92B
	SYS___RINT_H                        = 0x92C
	SYS___SCALB_H                       = 0x92D
	SYS___SINF_H                        = 0x92F
	SYS___SIN_H                         = 0x92E
	SYS_STRNCPY                         = 0x093
	SYS___TANHF_H                       = 0x93B
	SYS___TANHL_H                       = 0x93C
	SYS___TANH_H                        = 0x93A
	SYS___TGAMMAF_H                     = 0x93E
	SYS___TGAMMA_H                      = 0x93D
	SYS___TRUNC_H                       = 0x93F
	SYS_MEMCPY                          = 0x094
	SYS_VFWSCANF                        = 0x94A
	SYS_VSWSCANF                        = 0x94E
	SYS_VWSCANF                         = 0x94C
	SYS_INET6_RTH_ADD                   = 0x95D
	SYS_INET6_RTH_INIT                  = 0x95C
	SYS_INET6_RTH_REVERSE               = 0x95E
	SYS_INET6_RTH_SEGMENTS              = 0x95F
	SYS_INET6_RTH_SPACE                 = 0x95B
	SYS_MEMMOVE                         = 0x095
	SYS_WCSTOLD                         = 0x95A
	SYS_STRCPY                          = 0x096
	SYS_STRCMP                          = 0x097
	SYS_CABS                            = 0x98E
	SYS_STRCAT                          = 0x098
	SYS___CABS_B                        = 0x98F
	SYS___POW_II                        = 0x98A
	SYS___POW_II_B                      = 0x98B
	SYS___POW_II_H                      = 0x98C
	SYS_CACOSF                          = 0x99A
	SYS_CACOSL                          = 0x99D
	SYS_STRNCAT                         = 0x099
	SYS___CACOSF_B                      = 0x99B
	SYS___CACOSF_H                      = 0x99C
	SYS___CACOSL_B                      = 0x99E
	SYS___CACOSL_H                      = 0x99F
	SYS_ISWALPHA                        = 0x100
	SYS_ISWBLANK                        = 0x101
	SYS___ISWBLK                        = 0x101
	SYS_ISWCNTRL                        = 0x102
	SYS_ISWDIGIT                        = 0x103
	SYS_ISWGRAPH                        = 0x104
	SYS_ISWLOWER                        = 0x105
	SYS_ISWPRINT                        = 0x106
	SYS_ISWPUNCT                        = 0x107
	SYS_ISWSPACE                        = 0x108
	SYS_ISWUPPER                        = 0x109
	SYS_WCTOB                           = 0x110
	SYS_MBRLEN                          = 0x111
	SYS_MBRTOWC                         = 0x112
	SYS_MBSRTOWC                        = 0x113
	SYS_MBSRTOWCS                       = 0x113
	SYS_WCRTOMB                         = 0x114
	SYS_WCSRTOMB                        = 0x115
	SYS_WCSRTOMBS                       = 0x115
	SYS___CSID                          = 0x116
	SYS___WCSID                         = 0x117
	SYS_STRPTIME                        = 0x118
	SYS___STRPTM                        = 0x118
	SYS_STRFMON                         = 0x119
	SYS_WCSCOLL                         = 0x130
	SYS_WCSXFRM                         = 0x131
	SYS_WCSWIDTH                        = 0x132
	SYS_WCWIDTH                         = 0x133
	SYS_WCSFTIME                        = 0x134
	SYS_SWPRINTF                        = 0x135
	SYS_VSWPRINT                        = 0x136
	SYS_VSWPRINTF                       = 0x136
	SYS_SWSCANF                         = 0x137
	SYS_REGCOMP                         = 0x138
	SYS_REGEXEC                         = 0x139
	SYS_GETWC                           = 0x140
	SYS_GETWCHAR                        = 0x141
	SYS_PUTWC                           = 0x142
	SYS_PUTWCHAR                        = 0x143
	SYS_UNGETWC                         = 0x144
	SYS_ICONV_OPEN                      = 0x145
	SYS_ICONV                           = 0x146
	SYS_ICONV_CLOSE                     = 0x147
	SYS_COLLRANGE                       = 0x150
	SYS_CCLASS                          = 0x151
	SYS_COLLORDER                       = 0x152
	SYS___DEMANGLE                      = 0x154
	SYS_FDOPEN                          = 0x155
	SYS___ERRNO                         = 0x156
	SYS___ERRNO2                        = 0x157
	SYS___TERROR                        = 0x158
	SYS_MAXCOLL                         = 0x169
	SYS_DLLLOAD                         = 0x170
	SYS__EXIT                           = 0x174
	SYS_ACCESS                          = 0x175
	SYS_ALARM                           = 0x176
	SYS_CFGETISPEED                     = 0x177
	SYS_CFGETOSPEED                     = 0x178
	SYS_CFSETISPEED                     = 0x179
	SYS_CREAT                           = 0x180
	SYS_CTERMID                         = 0x181
	SYS_DUP                             = 0x182
	SYS_DUP2                            = 0x183
	SYS_EXECL                           = 0x184
	SYS_EXECLE                          = 0x185
	SYS_EXECLP                          = 0x186
	SYS_EXECV                           = 0x187
	SYS_EXECVE                          = 0x188
	SYS_EXECVP                          = 0x189
	SYS_FSTAT                           = 0x190
	SYS_FSYNC                           = 0x191
	SYS_FTRUNCATE                       = 0x192
	SYS_GETCWD                          = 0x193
	SYS_GETEGID                         = 0x194
	SYS_GETEUID                         = 0x195
	SYS_GETGID                          = 0x196
	SYS_GETGRGID                        = 0x197
	SYS_GETGRNAM                        = 0x198
	SYS_GETGROUPS                       = 0x199
	SYS_PTHREAD_MUTEXATTR_DESTROY       = 0x200
	SYS_PTHREAD_MUTEXATTR_SETKIND_NP    = 0x201
	SYS_PTHREAD_MUTEXATTR_GETKIND_NP    = 0x202
	SYS_PTHREAD_MUTEX_INIT              = 0x203
	SYS_PTHREAD_MUTEX_DESTROY           = 0x204
	SYS_PTHREAD_MUTEX_LOCK              = 0x205
	SYS_PTHREAD_MUTEX_TRYLOCK           = 0x206
	SYS_PTHREAD_MUTEX_UNLOCK            = 0x207
	SYS_PTHREAD_ONCE                    = 0x209
	SYS_TW_OPEN                         = 0x210
	SYS_TW_FCNTL                        = 0x211
	SYS_PTHREAD_JOIN_D4_NP              = 0x212
	SYS_PTHREAD_CONDATTR_SETKIND_NP     = 0x213
	SYS_PTHREAD_CONDATTR_GETKIND_NP     = 0x214
	SYS_EXTLINK_NP                      = 0x215
	SYS___PASSWD                        = 0x216
	SYS_SETGROUPS                       = 0x217
	SYS_INITGROUPS                      = 0x218
	SYS_WCSRCHR                         = 0x240
	SYS_SVC99                           = 0x241
	SYS___SVC99                         = 0x241
	SYS_WCSWCS                          = 0x242
	SYS_LOCALECO                        = 0x243
	SYS_LOCALECONV                      = 0x243
	SYS___LIBREL                        = 0x244
	SYS_RELEASE                         = 0x245
	SYS___RLSE                          = 0x245
	SYS_FLOCATE                         = 0x246
	SYS___FLOCT                         = 0x246
	SYS_FDELREC                         = 0x247
	SYS___FDLREC                        = 0x247
	SYS_FETCH                           = 0x248
	SYS___FETCH                         = 0x248
	SYS_QSORT                           = 0x249
	SYS___CLEANUPCATCH                  = 0x260
	SYS___CATCHMATCH                    = 0x261
	SYS___CLEAN2UPCATCH                 = 0x262
	SYS_GETPRIORITY                     = 0x270
	SYS_NICE                            = 0x271
	SYS_SETPRIORITY                     = 0x272
	SYS_GETITIMER                       = 0x273
	SYS_SETITIMER                       = 0x274
	SYS_MSGCTL                          = 0x275
	SYS_MSGGET                          = 0x276
	SYS_MSGRCV                          = 0x277
	SYS_MSGSND                          = 0x278
	SYS_MSGXRCV                         = 0x279
	SYS___MSGXR                         = 0x279
	SYS_SHMGET                          = 0x280
	SYS___GETIPC                        = 0x281
	SYS_SETGRENT                        = 0x282
	SYS_GETGRENT                        = 0x283
	SYS_ENDGRENT                        = 0x284
	SYS_SETPWENT                        = 0x285
	SYS_GETPWENT                        = 0x286
	SYS_ENDPWENT                        = 0x287
	SYS_BSD_SIGNAL                      = 0x288
	SYS_KILLPG                          = 0x289
	SYS_SIGSET                          = 0x290
	SYS_SIGSTACK                        = 0x291
	SYS_GETRLIMIT                       = 0x292
	SYS_SETRLIMIT                       = 0x293
	SYS_GETRUSAGE                       = 0x294
	SYS_MMAP                            = 0x295
	SYS_MPROTECT                        = 0x296
	SYS_MSYNC                           = 0x297
	SYS_MUNMAP                          = 0x298
	SYS_CONFSTR                         = 0x299
	SYS___NDMTRM                        = 0x300
	SYS_FTOK                            = 0x301
	SYS_BASENAME                        = 0x302
	SYS_DIRNAME                         = 0x303
	SYS_GETDTABLESIZE                   = 0x304
	SYS_MKSTEMP                         = 0x305
	SYS_MKTEMP                          = 0x306
	SYS_NFTW                            = 0x307
	SYS_GETWD                           = 0x308
	SYS_LOCKF                           = 0x309
	SYS_WORDEXP                         = 0x310
	SYS_WORDFREE                        = 0x311
	SYS_GETPGID                         = 0x312
	SYS_GETSID                          = 0x313
	SYS___UTMPXNAME                     = 0x314
	SYS_CUSERID                         = 0x315
	SYS_GETPASS                         = 0x316
	SYS_FNMATCH                         = 0x317
	SYS_FTW                             = 0x318
	SYS_GETW                            = 0x319
	SYS_ACOSH                           = 0x320
	SYS_ASINH                           = 0x321
	SYS_ATANH                           = 0x322
	SYS_CBRT                            = 0x323
	SYS_EXPM1                           = 0x324
	SYS_ILOGB                           = 0x325
	SYS_LOGB                            = 0x326
	SYS_LOG1P                           = 0x327
	SYS_NEXTAFTER                       = 0x328
	SYS_RINT                            = 0x329
	SYS_SPAWN                           = 0x330
	SYS_SPAWNP                          = 0x331
	SYS_GETLOGIN_UU                     = 0x332
	SYS_ECVT                            = 0x333
	SYS_FCVT                            = 0x334
	SYS_GCVT                            = 0x335
	SYS_ACCEPT                          = 0x336
	SYS_BIND                            = 0x337
	SYS_CONNECT                         = 0x338
	SYS_ENDHOSTENT                      = 0x339
	SYS_GETHOSTENT                      = 0x340
	SYS_GETHOSTID                       = 0x341
	SYS_GETHOSTNAME                     = 0x342
	SYS_GETNETBYADDR                    = 0x343
	SYS_GETNETBYNAME                    = 0x344
	SYS_GETNETENT                       = 0x345
	SYS_GETPEERNAME                     = 0x346
	SYS_GETPROTOBYNAME                  = 0x347
	SYS_GETPROTOBYNUMBER                = 0x348
	SYS_GETPROTOENT                     = 0x349
	SYS_INET_LNAOF                      = 0x350
	SYS_INET_MAKEADDR                   = 0x351
	SYS_INET_NETOF                      = 0x352
	SYS_INET_NETWORK                    = 0x353
	SYS_INET_NTOA                       = 0x354
	SYS_IOCTL                           = 0x355
	SYS_LISTEN                          = 0x356
	SYS_READV                           = 0x357
	SYS_RECV                            = 0x358
	SYS_RECVFROM                        = 0x359
	SYS_SETHOSTENT                      = 0x360
	SYS_SETNETENT                       = 0x361
	SYS_SETPEER                         = 0x362
	SYS_SETPROTOENT                     = 0x363
	SYS_SETSERVENT                      = 0x364
	SYS_SETSOCKOPT                      = 0x365
	SYS_SHUTDOWN                        = 0x366
	SYS_SOCKET                          = 0x367
	SYS_SOCKETPAIR                      = 0x368
	SYS_WRITEV                          = 0x369
	SYS_ENDNETENT                       = 0x370
	SYS_CLOSELOG                        = 0x371
	SYS_OPENLOG                         = 0x372
	SYS_SETLOGMASK                      = 0x373
	SYS_SYSLOG                          = 0x374
	SYS_PTSNAME                         = 0x375
	SYS_SETREUID                        = 0x376
	SYS_SETREGID                        = 0x377
	SYS_REALPATH                        = 0x378
	SYS___SIGNGAM                       = 0x379
	SYS_POLL                            = 0x380
	SYS_REXEC                           = 0x381
	SYS___ISASCII2                      = 0x382
	SYS___TOASCII2                      = 0x383
	SYS_CHPRIORITY                      = 0x384
	SYS_PTHREAD_ATTR_SETSYNCTYPE_NP     = 0x385
	SYS_PTHREAD_ATTR_GETSYNCTYPE_NP     = 0x386
	SYS_PTHREAD_SET_LIMIT_NP            = 0x387
	SYS___STNETENT                      = 0x388
	SYS___STPROTOENT                    = 0x389
	SYS___SELECT1                       = 0x390
	SYS_PTHREAD_SECURITY_NP             = 0x391
	SYS___CHECK_RESOURCE_AUTH_NP        = 0x392
	SYS___CONVERT_ID_NP                 = 0x393
	SYS___OPENVMREL                     = 0x394
	SYS_WMEMCHR                         = 0x395
	SYS_WMEMCMP                         = 0x396
	SYS_WMEMCPY                         = 0x397
	SYS_WMEMMOVE                        = 0x398
	SYS_WMEMSET                         = 0x399
	SYS___FPUTWC                        = 0x400
	SYS___PUTWC                         = 0x401
	SYS___PWCHAR                        = 0x402
	SYS___WCSFTM                        = 0x403
	SYS___WCSTOK                        = 0x404
	SYS___WCWDTH                        = 0x405
	SYS_T_ACCEPT                        = 0x409
	SYS_T_GETINFO                       = 0x410
	SYS_T_GETPROTADDR                   = 0x411
	SYS_T_GETSTATE                      = 0x412
	SYS_T_LISTEN                        = 0x413
	SYS_T_LOOK                          = 0x414
	SYS_T_OPEN                          = 0x415
	SYS_T_OPTMGMT                       = 0x416
	SYS_T_RCV                           = 0x417
	SYS_T_RCVCONNECT                    = 0x418
	SYS_T_RCVDIS                        = 0x419
	SYS_T_SNDUDATA                      = 0x420
	SYS_T_STRERROR                      = 0x421
	SYS_T_SYNC                          = 0x422
	SYS_T_UNBIND                        = 0x423
	SYS___T_ERRNO                       = 0x424
	SYS___RECVMSG2                      = 0x425
	SYS___SENDMSG2                      = 0x426
	SYS_FATTACH                         = 0x427
	SYS_FDETACH                         = 0x428
	SYS_GETMSG                          = 0x429
	SYS_GETCONTEXT                      = 0x430
	SYS_SETCONTEXT                      = 0x431
	SYS_MAKECONTEXT                     = 0x432
	SYS_SWAPCONTEXT                     = 0x433
	SYS_PTHREAD_GETSPECIFIC_D8_NP       = 0x434
	SYS_GETCLIENTID                     = 0x470
	SYS___GETCLIENTID                   = 0x471
	SYS_GETSTABLESIZE                   = 0x472
	SYS_GETIBMOPT                       = 0x473
	SYS_GETIBMSOCKOPT                   = 0x474
	SYS_GIVESOCKET                      = 0x475
	SYS_IBMSFLUSH                       = 0x476
	SYS_MAXDESC                         = 0x477
	SYS_SETIBMOPT                       = 0x478
	SYS_SETIBMSOCKOPT                   = 0x479
	SYS___SERVER_PWU                    = 0x480
	SYS_PTHREAD_TAG_NP                  = 0x481
	SYS___CONSOLE                       = 0x482
	SYS___WSINIT                        = 0x483
	SYS___IPTCPN                        = 0x489
	SYS___SERVER_CLASSIFY               = 0x490
	SYS___HEAPRPT                       = 0x496
	SYS___ISBFP                         = 0x500
	SYS___FP_CAST                       = 0x501
	SYS___CERTIFICATE                   = 0x502
	SYS_SEND_FILE                       = 0x503
	SYS_AIO_CANCEL                      = 0x504
	SYS_AIO_ERROR                       = 0x505
	SYS_AIO_READ                        = 0x506
	SYS_AIO_RETURN                      = 0x507
	SYS_AIO_SUSPEND                     = 0x508
	SYS_AIO_WRITE                       = 0x509
	SYS_PTHREAD_RWLOCK_TRYWRLOCK        = 0x510
	SYS_PTHREAD_RWLOCK_UNLOCK           = 0x511
	SYS_PTHREAD_RWLOCK_WRLOCK           = 0x512
	SYS_PTHREAD_RWLOCKATTR_GETPSHARED   = 0x513
	SYS_PTHREAD_RWLOCKATTR_SETPSHARED   = 0x514
	SYS_PTHREAD_RWLOCKATTR_INIT         = 0x515
	SYS_PTHREAD_RWLOCKATTR_DESTROY      = 0x516
	SYS___CTTBL                         = 0x517
	SYS_PTHREAD_MUTEXATTR_SETTYPE       = 0x518
	SYS_PTHREAD_MUTEXATTR_GETTYPE       = 0x519
	SYS___FP_UNORDERED                  = 0x520
	SYS___FP_READ_RND                   = 0x521
	SYS___FP_READ_RND_B                 = 0x522
	SYS___FP_SWAP_RND                   = 0x523
	SYS___FP_SWAP_RND_B                 = 0x524
	SYS___FP_LEVEL                      = 0x525
	SYS___FP_BTOH                       = 0x526
	SYS___FP_HTOB                       = 0x527
	SYS___FPC_RD                        = 0x528
	SYS___FPC_WR                        = 0x529
	SYS_PTHREAD_SETCANCELTYPE           = 0x600
	SYS_PTHREAD_TESTCANCEL              = 0x601
	SYS___ATANF_B                       = 0x602
	SYS___ATANL_B                       = 0x603
	SYS___CEILF_B                       = 0x604
	SYS___CEILL_B                       = 0x605
	SYS___COSF_B                        = 0x606
	SYS___COSL_B                        = 0x607
	SYS___FABSF_B                       = 0x608
	SYS___FABSL_B                       = 0x609
	SYS___SINF_B                        = 0x610
	SYS___SINL_B                        = 0x611
	SYS___TANF_B                        = 0x612
	SYS___TANL_B                        = 0x613
	SYS___TANHF_B                       = 0x614
	SYS___TANHL_B                       = 0x615
	SYS___ACOSF_B                       = 0x616
	SYS___ACOSL_B                       = 0x617
	SYS___ASINF_B                       = 0x618
	SYS___ASINL_B                       = 0x619
	SYS___LOGF_B                        = 0x620
	SYS___LOGL_B                        = 0x621
	SYS___LOG10F_B                      = 0x622
	SYS___LOG10L_B                      = 0x623
	SYS___POWF_B                        = 0x624
	SYS___POWL_B                        = 0x625
	SYS___SINHF_B                       = 0x626
	SYS___SINHL_B                       = 0x627
	SYS___SQRTF_B                       = 0x628
	SYS___SQRTL_B                       = 0x629
	SYS___MODFL_B                       = 0x630
	SYS_ABSF                            = 0x631
	SYS_ABSL                            = 0x632
	SYS_ACOSF                           = 0x633
	SYS_ACOSL                           = 0x634
	SYS_ASINF                           = 0x635
	SYS_ASINL                           = 0x636
	SYS_ATAN2F                          = 0x637
	SYS_ATAN2L                          = 0x638
	SYS_ATANF                           = 0x639
	SYS_COSHL                           = 0x640
	SYS_EXPF                            = 0x641
	SYS_EXPL                            = 0x642
	SYS_TANHF                           = 0x643
	SYS_TANHL                           = 0x644
	SYS_LOG10F                          = 0x645
	SYS_LOG10L                          = 0x646
	SYS_LOGF                            = 0x647
	SYS_LOGL                            = 0x648
	SYS_POWF                            = 0x649
	SYS_SINHL                           = 0x650
	SYS_TANF                            = 0x651
	SYS_TANL                            = 0x652
	SYS_FABSF                           = 0x653
	SYS_FABSL                           = 0x654
	SYS_FLOORF                          = 0x655
	SYS_FLOORL                          = 0x656
	SYS_FMODF                           = 0x657
	SYS_FMODL                           = 0x658
	SYS_FREXPF                          = 0x659
	SYS___CHATTR                        = 0x660
	SYS___FCHATTR                       = 0x661
	SYS___TOCCSID                       = 0x662
	SYS___CSNAMETYPE                    = 0x663
	SYS___TOCSNAME                      = 0x664
	SYS___CCSIDTYPE                     = 0x665
	SYS___AE_CORRESTBL_QUERY            = 0x666
	SYS___AE_AUTOCONVERT_STATE          = 0x667
	SYS_DN_FIND                         = 0x668
	SYS___GETHOSTBYADDR_A               = 0x669
	SYS___MBLEN_SB_A                    = 0x670
	SYS___MBLEN_STD_A                   = 0x671
	SYS___MBLEN_UTF                     = 0x672
	SYS___MBSTOWCS_A                    = 0x673
	SYS___MBSTOWCS_STD_A                = 0x674
	SYS___MBTOWC_A                      = 0x675
	SYS___MBTOWC_ISO1                   = 0x676
	SYS___MBTOWC_SBCS                   = 0x677
	SYS___MBTOWC_MBCS                   = 0x678
	SYS___MBTOWC_UTF                    = 0x679
	SYS___CSID_A                        = 0x680
	SYS___CSID_STD_A                    = 0x681
	SYS___WCSID_A                       = 0x682
	SYS___WCSID_STD_A                   = 0x683
	SYS___WCTOMB_A                      = 0x684
	SYS___WCTOMB_ISO1                   = 0x685
	SYS___WCTOMB_STD_A                  = 0x686
	SYS___WCTOMB_UTF                    = 0x687
	SYS___WCWIDTH_A                     = 0x688
	SYS___GETGRNAM_R_A                  = 0x689
	SYS___READDIR_R_A                   = 0x690
	SYS___E2A_S                         = 0x691
	SYS___FNMATCH_A                     = 0x692
	SYS___FNMATCH_C_A                   = 0x693
	SYS___EXECL_A                       = 0x694
	SYS___FNMATCH_STD_A                 = 0x695
	SYS___REGCOMP_A                     = 0x696
	SYS___REGCOMP_STD_A                 = 0x697
	SYS___REGERROR_A                    = 0x698
	SYS___REGERROR_STD_A                = 0x699
	SYS___SWPRINTF_A                    = 0x700
	SYS___FSCANF_A                      = 0x701
	SYS___SCANF_A                       = 0x702
	SYS___SSCANF_A                      = 0x703
	SYS___SWSCANF_A                     = 0x704
	SYS___ATOF_A                        = 0x705
	SYS___ATOI_A                        = 0x706
	SYS___ATOL_A                        = 0x707
	SYS___STRTOD_A                      = 0x708
	SYS___STRTOL_A                      = 0x709
	SYS___L64A_A                        = 0x710
	SYS___STRERROR_A                    = 0x711
	SYS___PERROR_A                      = 0x712
	SYS___FETCH_A                       = 0x713
	SYS___GETENV_A                      = 0x714
	SYS___MKSTEMP_A                     = 0x717
	SYS___PTSNAME_A                     = 0x718
	SYS___PUTENV_A                      = 0x719
	SYS___CHDIR_A                       = 0x720
	SYS___CHOWN_A                       = 0x721
	SYS___CHROOT_A                      = 0x722
	SYS___GETCWD_A                      = 0x723
	SYS___GETWD_A                       = 0x724
	SYS___LCHOWN_A                      = 0x725
	SYS___LINK_A                        = 0x726
	SYS___PATHCONF_A                    = 0x727
	SYS___IF_NAMEINDEX_A                = 0x728
	SYS___READLINK_A                    = 0x729
	SYS___EXTLINK_NP_A                  = 0x730
	SYS___ISALNUM_A                     = 0x731
	SYS___ISALPHA_A                     = 0x732
	SYS___A2E_S                         = 0x733
	SYS___ISCNTRL_A                     = 0x734
	SYS___ISDIGIT_A                     = 0x735
	SYS___ISGRAPH_A                     = 0x736
	SYS___ISLOWER_A                     = 0x737
	SYS___ISPRINT_A                     = 0x738
	SYS___ISPUNCT_A                     = 0x739
	SYS___ISWALPHA_A                    = 0x740
	SYS___A2E_L                         = 0x741
	SYS___ISWCNTRL_A                    = 0x742
	SYS___ISWDIGIT_A                    = 0x743
	SYS___ISWGRAPH_A                    = 0x744
	SYS___ISWLOWER_A                    = 0x745
	SYS___ISWPRINT_A                    = 0x746
	SYS___ISWPUNCT_A                    = 0x747
	SYS___ISWSPACE_A                    = 0x748
	SYS___ISWUPPER_A                    = 0x749
	SYS___REMOVE_A                      = 0x750
	SYS___RENAME_A                      = 0x751
	SYS___TMPNAM_A                      = 0x752
	SYS___FOPEN_A                       = 0x753
	SYS___FREOPEN_A                     = 0x754
	SYS___CUSERID_A                     = 0x755
	SYS___POPEN_A                       = 0x756
	SYS___TEMPNAM_A                     = 0x757
	SYS___FTW_A                         = 0x758
	SYS___GETGRENT_A                    = 0x759
	SYS___INET_NTOP_A                   = 0x760
	SYS___GETPASS_A                     = 0x761
	SYS___GETPWENT_A                    = 0x762
	SYS___GETPWNAM_A                    = 0x763
	SYS___GETPWUID_A                    = 0x764
	SYS_____CHECK_RESOURCE_AUTH_NP_A    = 0x765
	SYS___CHECKSCHENV_A                 = 0x766
	SYS___CONNECTSERVER_A               = 0x767
	SYS___CONNECTWORKMGR_A              = 0x768
	SYS_____CONSOLE_A                   = 0x769
	SYS___MSGSND_A                      = 0x770
	SYS___MSGXRCV_A                     = 0x771
	SYS___NFTW_A                        = 0x772
	SYS_____PASSWD_A                    = 0x773
	SYS___PTHREAD_SECURITY_NP_A         = 0x774
	SYS___QUERYMETRICS_A                = 0x775
	SYS___QUERYSCHENV                   = 0x776
	SYS___READV_A                       = 0x777
	SYS_____SERVER_CLASSIFY_A           = 0x778
	SYS_____SERVER_INIT_A               = 0x779
	SYS___W_GETPSENT_A                  = 0x780
	SYS___WRITEV_A                      = 0x781
	SYS___W_STATFS_A                    = 0x782
	SYS___W_STATVFS_A                   = 0x783
	SYS___FPUTC_A                       = 0x784
	SYS___PUTCHAR_A                     = 0x785
	SYS___PUTS_A                        = 0x786
	SYS___FGETS_A                       = 0x787
	SYS___GETS_A                        = 0x788
	SYS___FPUTS_A                       = 0x789
	SYS___PUTC_A                        = 0x790
	SYS___AE_THREAD_SETMODE             = 0x791
	SYS___AE_THREAD_SWAPMODE            = 0x792
	SYS___GETNETBYADDR_A                = 0x793
	SYS___GETNETBYNAME_A                = 0x794
	SYS___GETNETENT_A                   = 0x795
	SYS___GETPROTOBYNAME_A              = 0x796
	SYS___GETPROTOBYNUMBER_A            = 0x797
	SYS___GETPROTOENT_A                 = 0x798
	SYS___GETSERVBYNAME_A               = 0x799
	SYS_ACL_FIRST_ENTRY                 = 0x800
	SYS_ACL_GET_ENTRY                   = 0x801
	SYS_ACL_VALID                       = 0x802
	SYS_ACL_CREATE_ENTRY                = 0x803
	SYS_ACL_DELETE_ENTRY                = 0x804
	SYS_ACL_UPDATE_ENTRY                = 0x805
	SYS_ACL_DELETE_FD                   = 0x806
	SYS_ACL_DELETE_FILE                 = 0x807
	SYS_ACL_GET_FD                      = 0x808
	SYS_ACL_GET_FILE                    = 0x809
	SYS___ERFL_B                        = 0x810
	SYS___ERFCL_B                       = 0x811
	SYS___LGAMMAL_B                     = 0x812
	SYS___SETHOOKEVENTS                 = 0x813
	SYS_IF_NAMETOINDEX                  = 0x814
	SYS_IF_INDEXTONAME                  = 0x815
	SYS_IF_NAMEINDEX                    = 0x816
	SYS_IF_FREENAMEINDEX                = 0x817
	SYS_GETADDRINFO                     = 0x818
	SYS_GETNAMEINFO                     = 0x819
	SYS___DYNFREE_A                     = 0x820
	SYS___RES_QUERY_A                   = 0x821
	SYS___RES_SEARCH_A                  = 0x822
	SYS___RES_QUERYDOMAIN_A             = 0x823
	SYS___RES_MKQUERY_A                 = 0x824
	SYS___RES_SEND_A                    = 0x825
	SYS___DN_EXPAND_A                   = 0x826
	SYS___DN_SKIPNAME_A                 = 0x827
	SYS___DN_COMP_A                     = 0x828
	SYS___DN_FIND_A                     = 0x829
	SYS___INET_NTOA_A                   = 0x830
	SYS___INET_NETWORK_A                = 0x831
	SYS___ACCEPT_A                      = 0x832
	SYS___ACCEPT_AND_RECV_A             = 0x833
	SYS___BIND_A                        = 0x834
	SYS___CONNECT_A                     = 0x835
	SYS___GETPEERNAME_A                 = 0x836
	SYS___GETSOCKNAME_A                 = 0x837
	SYS___RECVFROM_A                    = 0x838
	SYS___SENDTO_A                      = 0x839
	SYS___LCHATTR                       = 0x840
	SYS___WRITEDOWN                     = 0x841
	SYS_PTHREAD_MUTEX_INIT2             = 0x842
	SYS___ACOSHF_B                      = 0x843
	SYS___ACOSHL_B                      = 0x844
	SYS___ASINHF_B                      = 0x845
	SYS___ASINHL_B                      = 0x846
	SYS___ATANHF_B                      = 0x847
	SYS___ATANHL_B                      = 0x848
	SYS___CBRTF_B                       = 0x849
	SYS___EXP2F_B                       = 0x850
	SYS___EXP2L_B                       = 0x851
	SYS___EXPM1F_B                      = 0x852
	SYS___EXPM1L_B                      = 0x853
	SYS___FDIMF_B                       = 0x854
	SYS___FDIM_B                        = 0x855
	SYS___FDIML_B                       = 0x856
	SYS___HYPOTF_B                      = 0x857
	SYS___HYPOTL_B                      = 0x858
	SYS___LOG1PF_B                      = 0x859
	SYS___REMQUOF_B                     = 0x860
	SYS___REMQUO_B                      = 0x861
	SYS___REMQUOL_B                     = 0x862
	SYS___TGAMMAF_B                     = 0x863
	SYS___TGAMMA_B                      = 0x864
	SYS___TGAMMAL_B                     = 0x865
	SYS___TRUNCF_B                      = 0x866
	SYS___TRUNC_B                       = 0x867
	SYS___TRUNCL_B                      = 0x868
	SYS___LGAMMAF_B                     = 0x869
	SYS_ASINHF                          = 0x870
	SYS_ASINHL                          = 0x871
	SYS_ATANHF                          = 0x872
	SYS_ATANHL                          = 0x873
	SYS_CBRTF                           = 0x874
	SYS_CBRTL                           = 0x875
	SYS_COPYSIGNF                       = 0x876
	SYS_CPYSIGNF                        = 0x876
	SYS_COPYSIGNL                       = 0x877
	SYS_CPYSIGNL                        = 0x877
	SYS_COTANF                          = 0x878
	SYS___COTANF                        = 0x878
	SYS_COTAN                           = 0x879
	SYS___COTAN                         = 0x879
	SYS_FDIM                            = 0x881
	SYS_FDIML                           = 0x882
	SYS_HYPOTF                          = 0x883
	SYS_HYPOTL                          = 0x884
	SYS_LOG1PF                          = 0x885
	SYS_LOG1PL                          = 0x886
	SYS_LOG2F                           = 0x887
	SYS_LOG2                            = 0x888
	SYS_LOG2L                           = 0x889
	SYS_TGAMMA                          = 0x890
	SYS_TGAMMAL                         = 0x891
	SYS_TRUNCF                          = 0x892
	SYS_TRUNC                           = 0x893
	SYS_TRUNCL                          = 0x894
	SYS_LGAMMAF                         = 0x895
	SYS_LGAMMAL                         = 0x896
	SYS_LROUNDF                         = 0x897
	SYS_LROUND                          = 0x898
	SYS_ERFF                            = 0x899
	SYS___COSHF_H                       = 0x900
	SYS___COSHL_H                       = 0x901
	SYS___COTAN_H                       = 0x902
	SYS___COTANF_H                      = 0x903
	SYS___COTANL_H                      = 0x904
	SYS___ERF_H                         = 0x905
	SYS___ERFF_H                        = 0x906
	SYS___ERFL_H                        = 0x907
	SYS___ERFC_H                        = 0x908
	SYS___ERFCF_H                       = 0x909
	SYS___FDIMF_H                       = 0x910
	SYS___FDIML_H                       = 0x911
	SYS___FMOD_H                        = 0x912
	SYS___FMODF_H                       = 0x913
	SYS___FMODL_H                       = 0x914
	SYS___GAMMA_H                       = 0x915
	SYS___HYPOT_H                       = 0x916
	SYS___ILOGB_H                       = 0x917
	SYS___LGAMMA_H                      = 0x918
	SYS___LGAMMAF_H                     = 0x919
	SYS___LOG2L_H                       = 0x920
	SYS___LOG1P_H                       = 0x921
	SYS___LOG10_H                       = 0x922
	SYS___LOG10F_H                      = 0x923
	SYS___LOG10L_H                      = 0x924
	SYS___LROUND_H                      = 0x925
	SYS___LROUNDF_H                     = 0x926
	SYS___NEXTAFTER_H                   = 0x927
	SYS___POW_H                         = 0x928
	SYS___POWF_H                        = 0x929
	SYS___SINL_H                        = 0x930
	SYS___SINH_H                        = 0x931
	SYS___SINHF_H                       = 0x932
	SYS___SINHL_H                       = 0x933
	SYS___SQRT_H                        = 0x934
	SYS___SQRTF_H                       = 0x935
	SYS___SQRTL_H                       = 0x936
	SYS___TAN_H                         = 0x937
	SYS___TANF_H                        = 0x938
	SYS___TANL_H                        = 0x939
	SYS___TRUNCF_H                      = 0x940
	SYS___TRUNCL_H                      = 0x941
	SYS___COSH_H                        = 0x942
	SYS___LE_DEBUG_SET_RESUME_MCH       = 0x943
	SYS_VFSCANF                         = 0x944
	SYS_VSCANF                          = 0x946
	SYS_VSSCANF                         = 0x948
	SYS_IMAXABS                         = 0x950
	SYS_IMAXDIV                         = 0x951
	SYS_STRTOIMAX                       = 0x952
	SYS_STRTOUMAX                       = 0x953
	SYS_WCSTOIMAX                       = 0x954
	SYS_WCSTOUMAX                       = 0x955
	SYS_ATOLL                           = 0x956
	SYS_STRTOF                          = 0x957
	SYS_STRTOLD                         = 0x958
	SYS_WCSTOF                          = 0x959
	SYS_INET6_RTH_GETADDR               = 0x960
	SYS_INET6_OPT_INIT                  = 0x961
	SYS_INET6_OPT_APPEND                = 0x962
	SYS_INET6_OPT_FINISH                = 0x963
	SYS_INET6_OPT_SET_VAL               = 0x964
	SYS_INET6_OPT_NEXT                  = 0x965
	SYS_INET6_OPT_FIND                  = 0x966
	SYS_INET6_OPT_GET_VAL               = 0x967
	SYS___POW_I                         = 0x987
	SYS___POW_I_B                       = 0x988
	SYS___POW_I_H                       = 0x989
	SYS___CABS_H                        = 0x990
	SYS_CABSF                           = 0x991
	SYS___CABSF_B                       = 0x992
	SYS___CABSF_H                       = 0x993
	SYS_CABSL                           = 0x994
	SYS___CABSL_B                       = 0x995
	SYS___CABSL_H                       = 0x996
	SYS_CACOS                           = 0x997
	SYS___CACOS_B                       = 0x998
	SYS___CACOS_H                       = 0x999
)
