%global debug_package %{nil}

Name: libteco-container-%{OS_CURRENT}
Version: %{VERSION}
Release: 1
Summary: tecorogin container runtime library
Group:   tecorigin
License: tecorigin proprietary
Source0: %{name}-%{version}.tar.gz 
%description
The teco-container library provides an interface to configure GNU/Linux
containers leveraging tecorigin hardware.

%prep
%setup -n %{name}-%{version}

%build

%install
mkdir -p %{buildroot}%{_bindir}
mkdir -p %{buildroot}%{_libdir}
cp -a ./usr/lib/* %{buildroot}%{_libdir}
cp -a ./usr/bin/* %{buildroot}%{_bindir}

%pre
%post
/sbin/ldconfig
%clean

%files
%{_libdir}/*
%{_bindir}/*

%changelog

