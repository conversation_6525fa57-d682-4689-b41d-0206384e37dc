
include $(MAKE_DIR)/common.mk

##### Source definitions #####
VERSION        := 495.44
PREFIX         := nvidia-modprobe-$(VERSION)
URL            := http://jfrog.tecorigin.net/artifactory/tecotp-tecodocker/nvidia-modprobe-495.44.tar.gz

SRCS_DIR       := $(DEPS_DIR)/src/$(PREFIX)
MODPROBE_UTILS := $(SRCS_DIR)/modprobe-utils

LIB_STATIC     := $(MODPROBE_UTILS)/libnvidia-modprobe-utils.a
LIB_INCS       := $(MODPROBE_UTILS)/nvidia-modprobe-utils.h \
                  $(MODPROBE_UTILS)/pci-enum.h
LIB_SRCS       := $(MODPROBE_UTILS)/nvidia-modprobe-utils.c \
                  $(MODPROBE_UTILS)/pci-sysfs.c

PATCH_FILE     := $(MAKE_DIR)/nvidia-modprobe.patch

##### Flags definitions #####

ARFLAGS  := -rU
CPPFLAGS := -D_FORTIFY_SOURCE=2 -DNV_LINUX
CFLAGS   := -O2 -g -fdata-sections -ffunction-sections -fstack-protector -fno-strict-aliasing -fPIC

##### Private rules #####

LIB_OBJS := $(LIB_SRCS:.c=.o)

$(SRCS_DIR)/.download_stamp:
	$(MKDIR) -p $(SRCS_DIR)
	$(CURL) --progress-bar -k --insecure -fSL $(URL)  | \
	$(TAR) -C $(SRCS_DIR) --strip-components=1 -xz $(PREFIX)/modprobe-utils
	$(PATCH) -d $(SRCS_DIR) -p1 < $(PATCH_FILE)
	@touch $@

$(LIB_SRCS): $(SRCS_DIR)/.download_stamp

##### Public rules #####

.PHONY: all install clean

all: $(LIB_STATIC)

$(LIB_STATIC): $(LIB_OBJS)
	$(AR) rs $@ $^

install: all
	$(INSTALL) -d -m 755 $(addprefix $(DESTDIR),$(includedir) $(libdir))
	$(INSTALL) -m 644 $(LIB_INCS) $(DESTDIR)$(includedir)
	$(INSTALL) -m 644 $(LIB_STATIC) $(DESTDIR)$(libdir)

clean:
	$(RM) $(LIB_OBJS) $(LIB_STATIC)
