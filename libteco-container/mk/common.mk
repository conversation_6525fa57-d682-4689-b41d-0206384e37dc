
##### Global definitions #####
MV       ?= mv -f
CP       ?= cp -a
GO       ?= go
LN       ?= ln
TAR      ?= tar
CURL     ?= curl
MKDIR    ?= mkdir
LDCONFIG ?= ldconfig
INSTALL  ?= install
STRIP    ?= strip
OBJCPY   ?= objcopy
RPCGEN   ?= rpcgen
BMAKE    ?= MAKEFLAGS= bmake
DOCKER   ?= docker
PATCH    ?= patch

UID      := $(shell id -u)
GID      := $(shell id -g)
DATE     := $(shell date -u --iso-8601=minutes)
REVISION ?= $(shell git rev-parse HEAD)
COMPILER := $(realpath $(shell which $(CC)))
PLATFORM ?= $(shell uname -m)
$(info PLATFORM = $(PLATFORM))

ifeq ($(DATE),)
$(error Invalid date format)
endif
ifeq ($(REVISION),)
$(error Invalid commit hash)
endif
ifeq ($(COMPILER),)
$(error Invalid compiler)
endif

##### Function definitions #####
getdef = $(shell sed -n "0,/$(1)/s/\#define\s\+$(1)\s\+\(\w*\)/\1/p" $(2))

ifeq ($(PLATFORM),x86_64)
getarch = $(shell [ -f /etc/debian_version ] && echo "amd64" || echo "x86_64")
else ifeq ($(PLATFORM),sw_64)
getarch = $(shell [ -f /etc/openEuler-release ] && echo "sw_64" || echo "sw_64")
else ifeq ($(PLATFORM),loongarch64)
getarch = $(shell [ -f /etc/anolis-release ] && echo "loongarch64" || echo "loongarch64")
else
$(error Unsupported architecture)
endif
