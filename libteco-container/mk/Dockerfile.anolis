ARG BASEIMAGE
FROM ${BASEIMAGE}

ARG OS_VERSION

RUN yum install -y \
    --setopt=best=0 \
    createrepo \
    bzip2 \
    elfutils-libelf-devel \
    file \
    gcc \
    git \
    libcap-devel \
    libseccomp-devel \
    libtirpc-devel \
    m4 \
    make \
    rpcgen \
    rpm-build \
    rpmlint \
    system-lsb-core \
    wget \
    which

RUN wget https://ftp.loongnix.cn/toolchain/golang/go-1.24/abi1.0/go1.24.3.linux-loong64.tar.gz -O /tmp/go1.24.3.linux-loong64.tar.gz && \
    tar -C /usr/local -xzf /tmp/go1.24.3.linux-loong64.tar.gz && \
    rm -f /tmp/go1.24.3.linux-loong64.tar.gz
ENV GOPATH /go
ENV PATH $GOPATH/bin:/usr/local/go/bin:$PATH

ARG WITH_NVCGO=no
ARG WITH_TIRPC=no
ARG WITH_SECCOMP=yes
ENV WITH_NVCGO=${WITH_NVCGO}
ENV WITH_TIRPC=${WITH_TIRPC}
ENV WITH_SECCOMP=${WITH_SECCOMP}

WORKDIR /tmp/libteco-container
COPY . .

ARG CFLAGS
ARG LDLIBS
ENV CFLAGS=${CFLAGS}
ENV LDLIBS=${LDLIBS}

ARG REVISION
ENV REVISION=${REVISION}
RUN make distclean && make -j"$(nproc)"

# Use the revision as the package version for the time being
ENV PKG_NAME libteco-container
ENV PKG_VERS ${REVISION}
ENV DIST_DIR=/tmp/${PKG_NAME}-${PKG_VERS}
RUN mkdir -p $DIST_DIR /dist

CMD bash -c " \
    make dist; \
    make rpm; \
    mv /tmp/${PKG_NAME}-${PKG_VERS}/*.rpm /dist; \
 "
