diff -ruN nvidia-modprobe-495.44/modprobe-utils/nvidia-modprobe-utils.c nvidia-modprobe-495.44-patched/modprobe-utils/nvidia-modprobe-utils.c
--- nvidia-modprobe-495.44/modprobe-utils/nvidia-modprobe-utils.c	2021-11-13 14:36:58.096684602 +0000
+++ nvidia-modprobe-495.44-patched/modprobe-utils/nvidia-modprobe-utils.c	2021-11-13 14:43:40.965146390 +0000
@@ -888,10 +888,10 @@
     return mknod_helper(major, minor_num, vgpu_dev_name, NV_PROC_REGISTRY_PATH);
 }
 
-static int nvidia_cap_get_device_file_attrs(const char* cap_file_path,
-                                            int *major,
-                                            int *minor,
-                                            char *name)
+int nvidia_cap_get_device_file_attrs(const char* cap_file_path,
+                                     int *major,
+                                     int *minor,
+                                     char *name)
 {
     char field[32];
     FILE *fp;
diff -ruN nvidia-modprobe-495.44/modprobe-utils/nvidia-modprobe-utils.h nvidia-modprobe-495.44-patched/modprobe-utils/nvidia-modprobe-utils.h
--- nvidia-modprobe-495.44/modprobe-utils/nvidia-modprobe-utils.h	2021-11-13 14:36:58.096684602 +0000
+++ nvidia-modprobe-495.44-patched/modprobe-utils/nvidia-modprobe-utils.h	2021-11-13 14:38:34.078700961 +0000
@@ -81,6 +81,7 @@
 int nvidia_nvswitch_get_file_state(int minor);
 int nvidia_cap_mknod(const char* cap_file_path, int *minor);
 int nvidia_cap_get_file_state(const char* cap_file_path);
+int nvidia_cap_get_device_file_attrs(const char* cap_file_path, int *major, int *minor, char *name);
 int nvidia_get_chardev_major(const char *name);
 int nvidia_msr_modprobe(void);
 
