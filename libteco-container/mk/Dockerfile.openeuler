ARG BASEIMAGE
FROM ${BASEIMAGE}

SHELL ["/bin/bash", "-c"]

ARG OS_VERSION
RUN if [ "${OS_VERSION}" = "8" ]; then \
        yum --enablerepo=powertools install -y \
            rpcgen \
            libseccomp-devel; \
    else \
        yum install -y \
            rpcgen \
            libseccomp-devel; \
    fi

RUN yum install -y \
        bzip2 \
        createrepo \
        elfutils-libelf-devel \
        gcc \
        git \
        libcap-devel \
        m4 \
        make \
        libarchive-devel \
        byacc-2.0.20210808-1 \
        elfutils-0.185-2 \
	libtirpc-devel \
        openeuler-lsb \
        libtirpc-devel \
        rpm-build \
        rpmlint

ARG OS_ARCH
ENV OS_ARCH=${OS_ARCH}
ENV GOPATH /go
ENV PATH $GOPATH/bin:/usr/local/go/bin:$PATH

ARG WITH_NVCGO=no
ARG WITH_TIRPC=no
ARG WITH_SECCOMP=yes
ENV WITH_NVCGO=${WITH_NVCGO}
ENV WITH_TIRPC=${WITH_TIRPC}
ENV WITH_SECCOMP=${WITH_SECCOMP}

WORKDIR /tmp/libteco-container
COPY . .

ARG CFLAGS
ARG LDLIBS
ENV CFLAGS=${CFLAGS}
ENV LDLIBS=${LDLIBS}

ARG REVISION
ENV REVISION=${REVISION}
RUN make distclean && make -j"$(nproc)"

# Use the revision as the package version for the time being
ENV PKG_NAME libteco-container
ENV PKG_VERS ${REVISION}
ENV DIST_DIR=/tmp/${PKG_NAME}-${PKG_VERS}
RUN mkdir -p $DIST_DIR /dist
CMD make dist && \
    make rpm && \
    mv /tmp/${PKG_NAME}-${PKG_VERS}/*.rpm /dist;
