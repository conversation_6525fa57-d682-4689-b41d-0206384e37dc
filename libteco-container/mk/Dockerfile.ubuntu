ARG BASEIMAGE
FROM ${BASEIMAGE}

SHELL ["/bin/bash", "-c"]

ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update && apt-get install -y --no-install-recommends \
        apt-utils \
        bmake \
        build-essential \
        bzip2 \
        ca-certificates \
        curl \
        devscripts \
        dh-make \
        fakeroot \
        git \
        libcap-dev \
        libelf-dev \
        libtirpc-dev \
        libseccomp-dev \
        lintian \
        lsb-release \
        m4 \
        wget \
        pkg-config \
        xz-utils && \
    rm -rf /var/lib/apt/lists/*

RUN wget http://jfrog.tecorigin.net/artifactory/tecotp-tecodocker/go1.17.1.linux-amd64.tar.gz && tar -C /usr/local -xzf go1.17.1.linux-amd64.tar.gz
ENV GOPATH /go
ENV PATH $GOPATH/bin:/usr/local/go/bin:$PATH

ENV GPG_TTY /dev/console

WORKDIR /tmp/libteco-container
COPY . .

ARG WITH_NVCGO=yes
ARG WITH_LIBELF=yes
ARG WITH_TIRPC=yes
ARG WITH_SECCOMP=yes
ENV WITH_NVCGO=${WITH_NVCGO}
ENV WITH_LIBELF=${WITH_LIBELF}
ENV WITH_TIRPC=${WITH_TIRPC}
ENV WITH_SECCOMP=${WITH_SECCOMP}

ARG REVISION
ENV REVISION=${REVISION}
ARG LIBNVIDIA_CONTAINER0_DEPENDENCY
ENV LIBNVIDIA_CONTAINER0_DEPENDENCY ${LIBNVIDIA_CONTAINER0_DEPENDENCY}

RUN make distclean && make -j"$(nproc)"

# Use the revision as the package version for the time being
ENV PKG_NAME libteco-container
ENV PKG_VERS ${REVISION}
ENV DIST_DIR=/tmp/${PKG_NAME}-${PKG_VERS}
RUN mkdir -p $DIST_DIR /dist

CMD bash -c " \
        export DISTRIB=$(lsb_release -c -s); \
        export SECTION="" \
        make dist; \
        make deb; \
        mv /tmp/${PKG_NAME}-${PKG_VERS}/*.deb /dist; \
     "
