#!/bin/sh
#
# $Id: native-elf-format 3293 2016-01-07 19:26:27Z emaste $
#
# Find the native ELF format for a host platform by compiling a
# test object and examining the resulting object.
#
# This script is used if there is no easy way to determine this
# information statically at compile time.

program=`basename $0`
tmp_c=`mktemp -u nefXXXXXX`.c
tmp_o=`echo ${tmp_c} | sed -e 's/.c$/.o/'`

trap "rm -f ${tmp_c} ${tmp_o}"	0 1 2 3 15

touch ${tmp_c}

echo "/* Generated by ${program} on `date` */"

cc -c ${tmp_c} -o ${tmp_o}
LC_ALL=C readelf -h ${tmp_o} | awk '
$1 ~ "Class:" {
        sub("ELF","",$2); elfclass = $2;
    }
$1 ~ "Data:"  {
        if (match($0, "little")) {
                elfdata = "LSB";
         } else {
                elfdata = "MSB";
        }
    }
$1 ~ "Machine:" {
        if (match($0, "Intel.*386")) {
            elfarch = "EM_386";
        } else if (match($0, "MIPS")) {
            elfarch = "EM_MIPS";
        } else if (match($0, ".*[xX]86-64")) {
            elfarch = "EM_X86_64";
        } else if (match($0, "AArch64")) {
            elfarch = "EM_X86_64";
        } else if (match($0, "Sw_64")) {
            elfarch = "EM_ALPHA";
        } else if (match($0, "loongarch64")) {
            elfarch = "EM_LOONGARCH";
        } else {
            elfarch = "unknown";
        }
    }
END {
    printf("#define	ELFTC_CLASS	ELFCLASS%s\n", elfclass);
    printf("#define	ELFTC_ARCH	%s\n", elfarch);
    printf("#define	ELFTC_BYTEORDER	ELFDATA2%s\n", elfdata);
}'

