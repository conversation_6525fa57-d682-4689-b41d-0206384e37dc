# Tecorigin Container Runtime Hook

## 概述

Tecorigin Container Runtime Hook 是一个用于容器运行时的钩子程序，旨在为使用 Tecorigin GPU 的容器提供必要的预启动配置。它是 Tecorigin Container Toolkit 的一部分，主要用于在容器生命周期的关键点执行特定操作。

## 功能特性

1. **GPU 设备配置**：根据环境变量和配置文件设置容器可见的 GPU 设备。
2. **驱动能力管理**：控制容器内可用的 NVIDIA 驱动功能。
3. **MIG 配置支持**：支持多实例 GPU (MIG) 的配置和监控。
4. **权限检查**：确保特权操作仅在适当的情况下执行。
5. **兼容性处理**：支持新旧版本 CUDA 镜像的平滑过渡。

## 主要组件

### capabilities.go
- 定义 [DriverCapabilities](file:///home/<USER>/workspace/teco-container/teco-container-toolkit/cmd/nvidia-container-runtime-hook/capabilities.go#L39-L39) 类型及其操作方法
- 实现驱动能力的交集计算、字符串解析等功能
- 提供将能力转换为命令行参数的功能

### container_config.go
- 定义容器配置相关的数据结构
- 实现从 OCI 规范文件加载配置的功能
- 处理特权检查、设备发现、驱动能力获取等核心逻辑
- 包含对 CUDA 版本、设备可见性、MIG 配置等的处理

### hook_config.go
- 定义钩子配置的数据结构
- 实现从 TOML 配置文件加载默认配置的功能
- 提供获取 Swarm 资源环境变量的方法

### main.go
- 程序入口点
- 实现 prestart 钩子的主要逻辑
- 负责调用 nvidia-container-cli 进行实际的容器配置

## 工作流程

1. 当容器运行时（如 Docker）启动一个容器时，会调用此钩子的 `prestart` 命令
2. 钩子读取配置文件（通常位于 `/etc/teco-container-runtime/teco-runtime-config.toml`）
3. 从 OCI 规范文件加载容器配置
4. 根据环境变量和配置确定需要暴露的 GPU 设备和驱动能力
5. 使用 `nvidia-container-cli` 对容器进行实际的 GPU 配置
6. 执行 exec 系统调用，将进程替换为容器的主进程

## 配置选项

可以在配置文件中设置以下选项：

- `disable-require`: 是否禁用要求检查
- `swarm-resource`: 指定用于 Swarm 的资源环境变量
- `accept-nvidia-visible-devices-envvar-when-unprivileged`: 是否允许非特权用户使用 NVIDIA_VISIBLE_DEVICES 环境变量
- `accept-nvidia-visible-devices-as-volume-mounts`: 是否允许通过卷挂载指定设备可见性
- `supported-driver-capabilities`: 支持的驱动能力列表

## 环境变量

支持以下环境变量来控制容器行为：

- `CUDA_VERSION`: 指定 CUDA 版本
- `NVIDIA_REQUIRE_CUDA`: 指定 CUDA 版本要求
- `NVIDIA_DISABLE_REQUIRE`: 是否禁用要求检查
- `TECO_VISIBLE_DEVICES`: 控制容器内可见的 GPU 设备
- `NVIDIA_MIG_CONFIG_DEVICES`: MIG 配置设备
- `NVIDIA_MIG_MONITOR_DEVICES`: MIG 监控设备
- `NVIDIA_DRIVER_CAPABILITIES`: 容器内可用的驱动能力

## 构建和安装

要构建和安装这个钩子，请按照以下步骤操作：

1. 确保已安装 Go 编译器和必要的构建工具
2. 运行 `make` 命令进行构建
3. 构建完成后，可使用 `make install` 安装到目标位置

## 使用示例

要手动测试钩子，可以使用以下命令：

```bash
# 显示帮助信息
./nvidia-container-runtime-hook -h

# 显示版本信息
./nvidia-container-runtime-hook -version

# 运行 prestart 钩子
./nvidia-container-runtime-hook prestart